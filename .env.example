# Environment Configuration
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Better Auth Configuration
BETTER_AUTH_SECRET=your_better_auth_secret_here
BETTER_AUTH_URL=http://localhost:3000

# Security Settings (Production)
# Set to 'false' in production to disable source maps
NEXT_PUBLIC_ENABLE_SOURCE_MAPS=true

# Application Settings
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Data Retention Settings (Optional)
# Enable automated data retention cleanup
DATA_RETENTION_ENABLED=true
# Maximum records to process in a single cleanup run (safety limit)
DATA_RETENTION_MAX_RECORDS=10000
# Enable dry run mode by default for safety
DATA_RETENTION_DRY_RUN_DEFAULT=true
