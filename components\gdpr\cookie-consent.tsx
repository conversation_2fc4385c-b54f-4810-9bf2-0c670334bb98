"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { <PERSON>, Settings, <PERSON>ie, Shield, BarChart3, Target } from "lucide-react";
import Link from "next/link";

interface CookieConsent {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
}

interface CookieConsentBannerProps {
  onConsentChange?: (consent: CookieConsent) => void;
}

export function CookieConsentBanner({ onConsentChange }: CookieConsentBannerProps) {
  const [showBanner, setShowBanner] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [consent, setConsent] = useState<CookieConsent>({
    necessary: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
  });

  useEffect(() => {
    // Check if user has already made a choice
    const savedConsent = localStorage.getItem('cookie-consent');
    if (!savedConsent) {
      setShowBanner(true);
    } else {
      const parsedConsent = JSON.parse(savedConsent);
      setConsent(parsedConsent);
      onConsentChange?.(parsedConsent);
    }
  }, [onConsentChange]);

  const saveConsent = (consentData: CookieConsent) => {
    localStorage.setItem('cookie-consent', JSON.stringify(consentData));
    localStorage.setItem('cookie-consent-date', new Date().toISOString());
    
    // Also save to database if user is logged in
    saveToDatabaseIfLoggedIn(consentData);
    
    setConsent(consentData);
    onConsentChange?.(consentData);
    setShowBanner(false);
    setShowSettings(false);
  };

  const saveToDatabaseIfLoggedIn = async (consentData: CookieConsent) => {
    try {
      // Only save to database if user is authenticated
      const response = await fetch('/api/gdpr/cookie-consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          necessary_cookies: consentData.necessary,
          analytics_cookies: consentData.analytics,
          marketing_cookies: consentData.marketing,
        }),
      });

      if (!response.ok && response.status !== 401) {
        console.error('Failed to save cookie consent to database');
      }
    } catch (error) {
      console.error('Error saving cookie consent:', error);
    }
  };

  const acceptAll = () => {
    saveConsent({
      necessary: true,
      analytics: true,
      marketing: true,
    });
  };

  const acceptNecessaryOnly = () => {
    saveConsent({
      necessary: true,
      analytics: false,
      marketing: false,
    });
  };

  const handleCustomSave = () => {
    saveConsent(consent);
  };

  if (!showBanner) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center p-4">
      <Card className="w-full max-w-2xl bg-neutral-900 border-neutral-700">
        <CardContent className="p-6">
          {!showSettings ? (
            // Simple consent banner
            <div>
              <div className="flex items-start gap-3 mb-4">
                <Cookie className="w-6 h-6 text-orange-400 mt-1 flex-shrink-0" />
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    We use cookies to enhance your experience
                  </h3>
                  <p className="text-neutral-300 text-sm mb-4">
                    We use cookies and similar technologies to provide essential website functionality, 
                    analyze usage, and improve your experience. You can customize your preferences or 
                    accept all cookies.
                  </p>
                  <p className="text-neutral-400 text-xs">
                    By clicking &quot;Accept All&quot;, you consent to our use of cookies.
                    Read our{' '}
                    <Link href="/cookies" className="text-blue-400 hover:underline">
                      Cookie Policy
                    </Link>{' '}
                    and{' '}
                    <Link href="/privacy" className="text-blue-400 hover:underline">
                      Privacy Policy
                    </Link>{' '}
                    for more information.
                  </p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={acceptAll}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Accept All Cookies
                </Button>
                <Button
                  onClick={acceptNecessaryOnly}
                  variant="outline"
                  className="border-neutral-600 text-neutral-300 hover:bg-neutral-800"
                >
                  Necessary Only
                </Button>
                <Button
                  onClick={() => setShowSettings(true)}
                  variant="ghost"
                  className="text-neutral-400 hover:text-white"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Customize
                </Button>
              </div>
            </div>
          ) : (
            // Detailed settings
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-white">Cookie Preferences</h3>
                <Button
                  onClick={() => setShowSettings(false)}
                  variant="ghost"
                  size="sm"
                  className="text-neutral-400 hover:text-white"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-6">
                {/* Necessary Cookies */}
                <div className="flex items-start gap-4">
                  <Shield className="w-5 h-5 text-green-400 mt-1 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-white">Necessary Cookies</h4>
                      <Switch
                        checked={true}
                        disabled={true}
                        className="data-[state=checked]:bg-green-600"
                      />
                    </div>
                    <p className="text-sm text-neutral-400">
                      Essential for website functionality, security, and user authentication. 
                      These cannot be disabled.
                    </p>
                  </div>
                </div>

                {/* Analytics Cookies */}
                <div className="flex items-start gap-4">
                  <BarChart3 className="w-5 h-5 text-blue-400 mt-1 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-white">Analytics Cookies</h4>
                      <Switch
                        checked={consent.analytics}
                        onCheckedChange={(checked) =>
                          setConsent(prev => ({ ...prev, analytics: checked }))
                        }
                        className="data-[state=checked]:bg-blue-600"
                      />
                    </div>
                    <p className="text-sm text-neutral-400">
                      Help us understand how you use our website so we can improve your experience 
                      and fix issues.
                    </p>
                  </div>
                </div>

                {/* Marketing Cookies */}
                <div className="flex items-start gap-4">
                  <Target className="w-5 h-5 text-purple-400 mt-1 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-white">Marketing Cookies</h4>
                      <Switch
                        checked={consent.marketing}
                        onCheckedChange={(checked) =>
                          setConsent(prev => ({ ...prev, marketing: checked }))
                        }
                        className="data-[state=checked]:bg-purple-600"
                      />
                    </div>
                    <p className="text-sm text-neutral-400">
                      Used to show you relevant advertisements and measure the effectiveness 
                      of our marketing campaigns.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 mt-6 pt-6 border-t border-neutral-700">
                <Button
                  onClick={handleCustomSave}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Save Preferences
                </Button>
                <Button
                  onClick={acceptAll}
                  variant="outline"
                  className="border-neutral-600 text-neutral-300 hover:bg-neutral-800"
                >
                  Accept All
                </Button>
              </div>

              <p className="text-xs text-neutral-500 mt-4">
                You can change these preferences at any time in your account settings or by 
                clicking the cookie settings link in our footer.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Hook to manage cookie consent state
export function useCookieConsent() {
  const [consent, setConsent] = useState<CookieConsent | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const savedConsent = localStorage.getItem('cookie-consent');
    if (savedConsent) {
      setConsent(JSON.parse(savedConsent));
    }
    setIsLoading(false);
  }, []);

  const updateConsent = (newConsent: CookieConsent) => {
    localStorage.setItem('cookie-consent', JSON.stringify(newConsent));
    setConsent(newConsent);
  };

  return {
    consent,
    isLoading,
    updateConsent,
    hasConsent: consent !== null,
    canUseAnalytics: consent?.analytics ?? false,
    canUseMarketing: consent?.marketing ?? false,
  };
}
