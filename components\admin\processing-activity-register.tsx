"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Shield, 
  Users, 
  Database, 
  Globe, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  Plus,
  Eye,
  BarChart3,
  Download,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';

interface ProcessingActivity {
  id: string;
  activity_name: string;
  controller_name: string;
  controller_contact: string;
  dpo_contact?: string;
  purposes: string[];
  legal_bases: string[];
  data_subject_categories: string[];
  personal_data_categories: string[];
  recipients?: string[];
  third_country_transfers?: any[];
  retention_periods: any[];
  security_measures: string[];
  system_generated: boolean;
  created_at: string;
  updated_at: string;
}

interface ProcessingActivitySummary {
  totalActivities: number;
  systemGenerated: number;
  userGenerated: number;
  legalBasisBreakdown: Record<string, number>;
  dataSubjectCategories: Record<string, number>;
  personalDataCategories: Record<string, number>;
  lastUpdated: number | null;
}

interface ProcessingActivityData {
  summary: ProcessingActivitySummary;
  activities: ProcessingActivity[];
}

export function ProcessingActivityRegister() {
  const [data, setData] = useState<ProcessingActivityData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitializing, setIsInitializing] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<ProcessingActivity | null>(null);
  const [viewMode, setViewMode] = useState<'summary' | 'detailed'>('summary');

  useEffect(() => {
    fetchProcessingActivities();
  }, []);

  const fetchProcessingActivities = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/processing-activities?format=detailed');
      
      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Admin access required');
          return;
        }
        throw new Error('Failed to fetch processing activities');
      }

      const result = await response.json();
      setData(result);
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch processing activities');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeStandardActivities = async () => {
    setIsInitializing(true);
    try {
      const response = await fetch('/api/admin/processing-activities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          initialize_standard: true
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to initialize standard activities');
      }

      toast.success('Standard processing activities initialized successfully');
      await fetchProcessingActivities(); // Refresh data
    } catch (error: any) {
      toast.error(error.message || 'Failed to initialize standard activities');
    } finally {
      setIsInitializing(false);
    }
  };

  const exportRegister = async () => {
    try {
      const response = await fetch('/api/admin/processing-activities?format=detailed');
      const result = await response.json();
      
      const exportData = {
        generated_at: new Date().toISOString(),
        controller: 'GenAForm SaaS Platform',
        contact: '<EMAIL>',
        activities: result.activities,
        summary: result.summary
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `processing-activity-register-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Processing activity register exported successfully');
    } catch (error: any) {
      toast.error('Failed to export register');
    }
  };

  const getLegalBasisBadge = (basis: string) => {
    const colors: Record<string, string> = {
      'consent': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'contract': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      'legal_obligation': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      'vital_interests': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      'public_task': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      'legitimate_interests': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
    };

    return (
      <Badge className={colors[basis] || 'bg-gray-100 text-gray-800'}>
        {basis.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Loading processing activity register...</span>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center p-8">
        <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-yellow-500" />
        <p>Failed to load processing activity register</p>
        <Button onClick={fetchProcessingActivities} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Processing Activity Register</h2>
          <p className="text-muted-foreground">
            GDPR Article 30 compliance - Record of all data processing activities
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchProcessingActivities}
            disabled={isLoading}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={exportRegister}
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button
            onClick={initializeStandardActivities}
            disabled={isInitializing}
          >
            {isInitializing ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Plus className="w-4 h-4 mr-2" />
            )}
            Initialize Standard Activities
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.totalActivities}</div>
            <p className="text-xs text-muted-foreground">
              {data.summary.systemGenerated} system, {data.summary.userGenerated} custom
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Legal Bases</CardTitle>
            <Shield className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.keys(data.summary.legalBasisBreakdown).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Different legal bases used
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Categories</CardTitle>
            <Database className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.keys(data.summary.personalDataCategories).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Personal data categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Updated</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-sm">
              {data.summary.lastUpdated 
                ? new Date(data.summary.lastUpdated).toLocaleDateString()
                : 'Never'
              }
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activities List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Processing Activities</span>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'summary' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('summary')}
              >
                Summary
              </Button>
              <Button
                variant={viewMode === 'detailed' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('detailed')}
              >
                Detailed
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Complete record of all data processing activities as required by GDPR Article 30
          </CardDescription>
        </CardHeader>
        <CardContent>
          {data.activities.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="w-8 h-8 mx-auto mb-2" />
              <p>No processing activities found</p>
              <Button 
                onClick={initializeStandardActivities} 
                className="mt-4"
                disabled={isInitializing}
              >
                Initialize Standard Activities
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {data.activities.map((activity) => (
                <div key={activity.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">{activity.activity_name}</h3>
                      <p className="text-sm text-muted-foreground">
                        Controller: {activity.controller_name}
                      </p>
                      {activity.system_generated && (
                        <Badge variant="secondary" className="mt-1">
                          System Generated
                        </Badge>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedActivity(activity)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </div>

                  <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Purposes</h4>
                      <div className="flex flex-wrap gap-1">
                        {activity.purposes.slice(0, 2).map((purpose, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {purpose}
                          </Badge>
                        ))}
                        {activity.purposes.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{activity.purposes.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-1">Legal Bases</h4>
                      <div className="flex flex-wrap gap-1">
                        {activity.legal_bases.slice(0, 2).map((basis, index) => (
                          <span key={index}>
                            {getLegalBasisBadge(basis)}
                          </span>
                        ))}
                        {activity.legal_bases.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{activity.legal_bases.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-1">Data Categories</h4>
                      <div className="flex flex-wrap gap-1">
                        {activity.personal_data_categories.slice(0, 2).map((category, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {category.replace('_', ' ')}
                          </Badge>
                        ))}
                        {activity.personal_data_categories.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{activity.personal_data_categories.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t text-xs text-muted-foreground">
                    Created: {new Date(activity.created_at).toLocaleDateString()} • 
                    Updated: {new Date(activity.updated_at).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Activity Detail Modal */}
      {selectedActivity && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">{selectedActivity.activity_name}</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedActivity(null)}
                >
                  Close
                </Button>
              </div>

              <div className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h3 className="font-medium mb-3">Controller Information</h3>
                  <div className="grid gap-3 md:grid-cols-2">
                    <div>
                      <label className="text-sm font-medium">Controller Name</label>
                      <p className="text-sm text-muted-foreground">{selectedActivity.controller_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Contact</label>
                      <p className="text-sm text-muted-foreground">{selectedActivity.controller_contact}</p>
                    </div>
                    {selectedActivity.dpo_contact && (
                      <div>
                        <label className="text-sm font-medium">DPO Contact</label>
                        <p className="text-sm text-muted-foreground">{selectedActivity.dpo_contact}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Purposes */}
                <div>
                  <h3 className="font-medium mb-3">Purposes of Processing</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedActivity.purposes.map((purpose, index) => (
                      <Badge key={index} variant="outline">
                        {purpose}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Legal Bases */}
                <div>
                  <h3 className="font-medium mb-3">Legal Bases</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedActivity.legal_bases.map((basis, index) => (
                      <span key={index}>
                        {getLegalBasisBadge(basis)}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Data Categories */}
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <h3 className="font-medium mb-3">Data Subject Categories</h3>
                    <div className="space-y-1">
                      {selectedActivity.data_subject_categories.map((category, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Personal Data Categories</h3>
                    <div className="space-y-1">
                      {selectedActivity.personal_data_categories.map((category, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Database className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Recipients */}
                {selectedActivity.recipients && selectedActivity.recipients.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-3">Recipients</h3>
                    <div className="space-y-1">
                      {selectedActivity.recipients.map((recipient, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Globe className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{recipient}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Third Country Transfers */}
                {selectedActivity.third_country_transfers && selectedActivity.third_country_transfers.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-3">International Transfers</h3>
                    <div className="space-y-3">
                      {selectedActivity.third_country_transfers.map((transfer, index) => (
                        <div key={index} className="border rounded-lg p-3">
                          <div className="grid gap-2 md:grid-cols-2">
                            <div>
                              <label className="text-sm font-medium">Country</label>
                              <p className="text-sm text-muted-foreground">{transfer.country}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium">Recipient</label>
                              <p className="text-sm text-muted-foreground">{transfer.recipient}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium">Purpose</label>
                              <p className="text-sm text-muted-foreground">{transfer.purpose}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium">Adequacy Decision</label>
                              <Badge variant={transfer.adequacy_decision ? "default" : "destructive"}>
                                {transfer.adequacy_decision ? "Yes" : "No"}
                              </Badge>
                            </div>
                          </div>
                          {transfer.safeguards && transfer.safeguards.length > 0 && (
                            <div className="mt-2">
                              <label className="text-sm font-medium">Safeguards</label>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {transfer.safeguards.map((safeguard: string, idx: number) => (
                                  <Badge key={idx} variant="outline" className="text-xs">
                                    {safeguard}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Retention Periods */}
                <div>
                  <h3 className="font-medium mb-3">Retention Periods</h3>
                  <div className="space-y-3">
                    {selectedActivity.retention_periods.map((period, index) => (
                      <div key={index} className="border rounded-lg p-3">
                        <div className="grid gap-2 md:grid-cols-3">
                          <div>
                            <label className="text-sm font-medium">Data Category</label>
                            <p className="text-sm text-muted-foreground">
                              {period.data_category?.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium">Retention Period</label>
                            <p className="text-sm text-muted-foreground">{period.retention_period}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium">Deletion Method</label>
                            <Badge variant="outline">{period.deletion_method}</Badge>
                          </div>
                        </div>
                        <div className="mt-2">
                          <label className="text-sm font-medium">Legal Basis</label>
                          <p className="text-sm text-muted-foreground">{period.legal_basis}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Security Measures */}
                <div>
                  <h3 className="font-medium mb-3">Security Measures</h3>
                  <div className="space-y-1">
                    {selectedActivity.security_measures.map((measure, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Shield className="w-4 h-4 text-green-500" />
                        <span className="text-sm">{measure}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Metadata */}
                <div className="pt-4 border-t">
                  <div className="grid gap-2 md:grid-cols-3 text-sm text-muted-foreground">
                    <div>
                      <label className="font-medium">Created</label>
                      <p>{new Date(selectedActivity.created_at).toLocaleString()}</p>
                    </div>
                    <div>
                      <label className="font-medium">Updated</label>
                      <p>{new Date(selectedActivity.updated_at).toLocaleString()}</p>
                    </div>
                    <div>
                      <label className="font-medium">Type</label>
                      <p>{selectedActivity.system_generated ? 'System Generated' : 'User Created'}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
