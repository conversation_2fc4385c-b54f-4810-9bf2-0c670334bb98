#!/usr/bin/env tsx

/**
 * Automated Data Retention Cleanup Script
 * 
 * This script can be run as a scheduled job (cron) to automatically
 * clean up data according to retention policies.
 * 
 * Usage:
 *   bun run scripts/data-retention-cleanup.ts [--dry-run] [--table=tablename] [--force]
 * 
 * Examples:
 *   bun run scripts/data-retention-cleanup.ts --dry-run
 *   bun run scripts/data-retention-cleanup.ts --table=sessions
 *   bun run scripts/data-retention-cleanup.ts --force
 */

import { 
  getDataRetentionStatus, 
  executeRetentionCleanup, 
  DATA_RETENTION_POLICIES,
  getRetentionPolicy 
} from '../lib/data-retention';
import { supabaseAdmin } from '../lib/supabase';

interface CleanupOptions {
  dryRun: boolean;
  specificTable?: string;
  force: boolean;
  verbose: boolean;
}

/**
 * Parse command line arguments
 */
function parseArguments(): CleanupOptions {
  const args = process.argv.slice(2);
  
  return {
    dryRun: args.includes('--dry-run'),
    specificTable: args.find(arg => arg.startsWith('--table='))?.split('=')[1],
    force: args.includes('--force'),
    verbose: args.includes('--verbose') || args.includes('-v')
  };
}

/**
 * Log with timestamp
 */
function log(message: string, level: 'info' | 'warn' | 'error' = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

/**
 * Main cleanup execution
 */
async function runDataRetentionCleanup(options: CleanupOptions): Promise<void> {
  log(`Starting data retention cleanup ${options.dryRun ? '(DRY RUN)' : ''}`);
  
  try {
    // Get current retention status
    const retentionStatus = await getDataRetentionStatus();
    
    log(`Found ${retentionStatus.policies.length} retention policies`);
    log(`Total records eligible for cleanup: ${retentionStatus.totalRecordsToCleanup}`);

    if (retentionStatus.totalRecordsToCleanup === 0) {
      log('No data cleanup needed at this time');
      return;
    }

    // Filter policies based on options
    let policiesToProcess = retentionStatus.policies.filter(p => p.cleanupNeeded);
    
    if (options.specificTable) {
      policiesToProcess = policiesToProcess.filter(p => p.table === options.specificTable);
      if (policiesToProcess.length === 0) {
        log(`No cleanup needed for table: ${options.specificTable}`, 'warn');
        return;
      }
    }

    // Safety check for large deletions
    const totalRecordsToProcess = policiesToProcess.reduce((sum, p) => sum + p.recordCount, 0);
    
    if (totalRecordsToProcess > 10000 && !options.force && !options.dryRun) {
      log(`Large deletion detected (${totalRecordsToProcess} records). Use --force to proceed or --dry-run to preview.`, 'warn');
      return;
    }

    // Process each policy
    const results = [];
    let totalProcessed = 0;

    for (const policyStatus of policiesToProcess) {
      const policy = getRetentionPolicy(policyStatus.table);
      if (!policy) continue;

      log(`Processing ${policyStatus.table}: ${policyStatus.recordCount} records (${policy.deletionMethod})`);

      if (options.dryRun) {
        log(`  [DRY RUN] Would ${policy.deletionMethod} ${policyStatus.recordCount} records older than ${policyStatus.cutoffDate.toISOString()}`);
        results.push({
          table: policyStatus.table,
          success: true,
          recordsProcessed: 0,
          wouldProcess: policyStatus.recordCount
        });
      } else {
        const result = await executeRetentionCleanup(policy);
        
        if (result.success) {
          log(`  ✅ Successfully processed ${result.recordsProcessed} records from ${policyStatus.table}`);
          totalProcessed += result.recordsProcessed;
        } else {
          log(`  ❌ Failed to process ${policyStatus.table}: ${result.error}`, 'error');
        }

        results.push({
          table: policyStatus.table,
          success: result.success,
          recordsProcessed: result.recordsProcessed,
          error: result.error
        });
      }
    }

    // Log summary
    if (options.dryRun) {
      const wouldProcess = results.reduce((sum, r) => sum + (r.wouldProcess || 0), 0);
      log(`DRY RUN COMPLETE: Would process ${wouldProcess} records across ${results.length} tables`);
    } else {
      const successful = results.filter(r => r.success).length;
      log(`CLEANUP COMPLETE: Processed ${totalProcessed} records across ${successful}/${results.length} tables`);
    }

    // Log the cleanup run to audit logs
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: null, // System action
        action: options.dryRun ? 'scheduled_retention_dry_run' : 'scheduled_retention_cleanup',
        resource_type: 'data_retention',
        details: {
          script_run: true,
          dry_run: options.dryRun,
          specific_table: options.specificTable,
          force: options.force,
          total_records_processed: totalProcessed,
          policies_processed: policiesToProcess.length,
          results: results
        }
      });

    if (options.verbose) {
      log('Detailed results:');
      results.forEach(result => {
        log(`  ${result.table}: ${result.success ? '✅' : '❌'} ${result.recordsProcessed || result.wouldProcess || 0} records`);
        if (result.error) {
          log(`    Error: ${result.error}`, 'error');
        }
      });
    }

  } catch (error: any) {
    log(`Cleanup failed: ${error.message}`, 'error');
    
    // Log the error
    try {
      await supabaseAdmin
        .from('audit_logs')
        .insert({
          user_id: null,
          action: 'scheduled_retention_error',
          resource_type: 'data_retention',
          details: {
            error: error.message,
            stack: error.stack,
            options
          }
        });
    } catch (logError) {
      log(`Failed to log error: ${logError}`, 'error');
    }
    
    process.exit(1);
  }
}

/**
 * Display help information
 */
function showHelp() {
  console.log(`
Data Retention Cleanup Script

Usage: bun run scripts/data-retention-cleanup.ts [options]

Options:
  --dry-run           Preview what would be cleaned up without making changes
  --table=<name>      Only process the specified table
  --force             Proceed with large deletions without confirmation
  --verbose, -v       Show detailed results
  --help, -h          Show this help message

Examples:
  bun run scripts/data-retention-cleanup.ts --dry-run
  bun run scripts/data-retention-cleanup.ts --table=sessions
  bun run scripts/data-retention-cleanup.ts --force --verbose

Retention Policies:
${DATA_RETENTION_POLICIES.map(policy => 
  `  ${policy.table}: ${policy.retentionPeriodDays === -1 ? 'Never delete' : policy.retentionPeriodDays + ' days'} (${policy.deletionMethod})`
).join('\n')}
`);
}

/**
 * Main entry point
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const options = parseArguments();
  
  log('Data Retention Cleanup Script Starting');
  log(`Options: ${JSON.stringify(options)}`);
  
  await runDataRetentionCleanup(options);
  
  log('Script completed successfully');
}

// Run the script if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

export { runDataRetentionCleanup, parseArguments };
