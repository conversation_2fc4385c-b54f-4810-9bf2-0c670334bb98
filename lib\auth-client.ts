import { createAuthClient } from "better-auth/react";

/**
 * Better Auth client configuration
 * This client is used on the frontend to interact with the Better Auth API
 * It automatically handles authentication state, session management, and API calls
 */
export const authClient = createAuthClient({
  baseURL: process.env.BETTER_AUTH_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'),
  fetchOptions: {
    onError: (e) => {
      if (e.error.status === 429) {
        console.warn("Rate limited. Please try again later.");
      }
    },
  },
});
