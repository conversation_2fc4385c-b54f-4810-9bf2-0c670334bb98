import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * POST /api/gdpr/restriction
 * 
 * GDPR Article 18 - Right to Restriction of Processing
 * Handle user requests to restrict processing of their personal data
 * 
 * This endpoint allows users to request restrictions on how their data is processed.
 * Under GDPR Article 18, users can restrict processing in specific circumstances:
 * - When data accuracy is contested
 * - When processing is unlawful but user doesn't want erasure
 * - When data is no longer needed but required for legal claims
 * - When user has objected and verification of legitimate grounds is pending
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate the user using session from request headers
    // Only authenticated users can make restriction requests
    const session = await auth.api.getSession({ headers: headers() });
    
    // Return 401 Unauthorized if no valid session exists
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    // Parse the JSON request body to extract restriction request details
    const body = await request.json();
    const { 
      restriction_type,     // Type of restriction ('temporary', 'permanent', 'conditional', 'marketing_communications')
      data_categories,      // Categories of data to restrict (array of strings)
      reason,              // User's reason for requesting restriction
      processing_purposes  // Specific processing purposes to restrict (optional)
    } = body;

    // Validate that all required fields are present in the request
    // These fields are mandatory for processing any restriction request
    if (!restriction_type || !data_categories || !reason) {
      return NextResponse.json(
        { error: "Missing required fields: restriction_type, data_categories, reason" },
        { status: 400 }
      );
    }

    // Extract user and request metadata for audit logging and security
    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';  // User's IP address
    const userAgent = request.headers.get('user-agent') || 'unknown';      // User's browser/client info

    // Create a structured restriction request object for audit logging
    // This contains all necessary information to track and process the restriction
    const restrictionRequest = {
      user_id: userId,
      request_type: 'restriction',
      status: 'pending',                                                    // Initial status
      details: {
        restriction_type,                                                   // Type of restriction requested
        // Ensure data_categories is always an array for consistent processing
        data_categories: Array.isArray(data_categories) ? data_categories : [data_categories],
        // Ensure processing_purposes is always an array (can be empty)
        processing_purposes: Array.isArray(processing_purposes) ? processing_purposes : [processing_purposes],
        reason,                                                             // User's stated reason
        grounds: getRestrictionGrounds(reason)                              // Legal grounds determined from reason
      },
      ip_address: clientIP,                                                 // For security audit trail
      user_agent: userAgent                                                 // For security audit trail
    };

    // Insert the restriction request into the audit_logs table
    // This creates a permanent record of the user's request for compliance purposes
    const { data, error } = await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'restriction_request',                                      // Action type for filtering
        resource_type: 'data_processing',                                   // Type of resource being restricted
        details: restrictionRequest.details,                                // Full request details
        ip_address: clientIP,
        user_agent: userAgent
      })
      .select()                                                             // Return the inserted record
      .single();                                                            // Expect only one record

    // If database insertion fails, throw error to be caught by outer try-catch
    if (error) {
      throw error;
    }

    // Handle certain restriction types that can be implemented immediately
    // Marketing communications restrictions can be applied automatically
    if (restriction_type === 'marketing_communications') {
      try {
        // Update user consent records to withdraw marketing consent
        // This immediately stops marketing communications
        await supabaseAdmin
          .from('user_consents')
          .update({ withdrawal_date: new Date().toISOString() })            // Mark consent as withdrawn
          .eq('user_id', userId)                                            // Only for this user
          .eq('consent_type', 'marketing_emails')                           // Specifically marketing emails
          .is('withdrawal_date', null);                                     // Only active consents

        // Create an audit log entry for the immediate restriction application
        // This documents that the restriction was applied automatically
        await supabaseAdmin
          .from('audit_logs')
          .insert({
            user_id: userId,
            action: 'restriction_applied',                                  // Mark as applied
            resource_type: 'marketing_processing',                          // Type of processing restricted
            details: {
              restriction_type: 'marketing_communications',                 // Specific restriction type
              applied_immediately: true,                                    // Flag for immediate application
              affected_processing: ['marketing_emails', 'promotional_communications'] // What was restricted
            },
            ip_address: clientIP,
            user_agent: userAgent
          });

        // Return success response with immediate completion status
        return NextResponse.json({
          success: true,
          message: "Processing restriction applied successfully",
          request_id: data.id,                                              // Reference to original request
          status: 'completed',                                              // Status is completed immediately
          immediate_action: true,                                           // Flag indicating immediate action
          restricted_processing: ['marketing_communications']               // What processing was restricted
        });
      } catch (restrictionError) {
        // If immediate restriction fails, log the error but continue with manual review
        // This ensures the user's request isn't lost even if auto-restriction fails
        console.error('Immediate restriction failed:', restrictionError);
        // Continue with manual review process below
      }
    }

    // Log the creation of the restriction request for monitoring purposes
    console.log(`Restriction request created for user: ${userId}, type: ${restriction_type}`);

    // Return response for requests that require manual review
    // GDPR requires organizations to respond within 30 days (can be extended to 90 days in complex cases)
    return NextResponse.json({
      success: true,
      message: "Processing restriction request submitted successfully. We will review and implement your request within 30 days.",
      request_id: data.id,                                                  // Unique ID for tracking the request
      status: 'pending',                                                    // Status indicates manual review needed
      estimated_completion: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now (GDPR compliance)
      next_steps: [                                                         // Clear communication about what happens next
        "We will review your request and legal grounds",
        "If approved, processing restrictions will be implemented",
        "You will be notified of any decisions regarding your data"
      ]
    });

  } catch (error: any) {
    // Handle any errors that occur during the restriction request process
    // Log the error for debugging while returning a generic error to the user
    console.error('Restriction request error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/gdpr/restriction
 * 
 * Get user's restriction request history and current restrictions
 * 
 * This endpoint allows users to view all their restriction requests and see
 * which restrictions are currently active. This transparency is important
 * for GDPR compliance and user control over their data.
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user to ensure they can only see their own restrictions
    const session = await auth.api.getSession({ headers: headers() });
    
    // Return 401 if user is not authenticated
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Query the audit_logs table to get all restriction-related entries for this user
    // We look for requests, applications, and lifting of restrictions
    const { data: requests, error } = await supabaseAdmin
      .from('audit_logs')
      .select('*')                                                          // Get all columns
      .eq('user_id', userId)                                                // Only this user's requests
      .in('action', ['restriction_request', 'restriction_applied', 'restriction_lifted']) // All restriction actions
      .order('created_at', { ascending: false });                          // Most recent first

    // Handle database query errors
    if (error) {
      throw error;
    }

    // Process the requests to identify currently active restrictions
    // Active restrictions are those that have been applied but not yet lifted
    const activeRestrictions = (requests || [])
      .filter(req => req.action === 'restriction_applied')                  // Only applied restrictions
      .map(req => ({
        id: req.id,                                                         // Unique identifier
        restriction_type: req.details?.restriction_type,                    // Type of restriction
        applied_date: req.created_at,                                       // When it was applied
        affected_processing: req.details?.affected_processing || []         // What processing is restricted
      }));

    // Return comprehensive restriction information
    return NextResponse.json({
      success: true,
      requests: requests || [],                                             // All restriction-related requests
      active_restrictions: activeRestrictions,                              // Currently active restrictions
      restriction_status: activeRestrictions.length > 0 ? 'active' : 'none' // Overall status summary
    });

  } catch (error: any) {
    // Handle any errors that occur while fetching restriction information
    console.error('Get restriction requests error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/gdpr/restriction
 * 
 * Lift/remove existing processing restrictions
 * 
 * This endpoint allows users to remove restrictions they previously placed
 * on their data processing. This gives users full control over their restrictions.
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate the user to ensure they can only lift their own restrictions
    const session = await auth.api.getSession({ headers: headers() });
    
    // Return 401 if user is not authenticated
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    // Parse the request body to get restriction details
    const body = await request.json();
    const { restriction_id, reason } = body;                                // ID of restriction to lift and optional reason

    // Extract user and request metadata for audit logging
    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';  // User's IP address
    const userAgent = request.headers.get('user-agent') || 'unknown';      // User's browser/client info

    // Create an audit log entry for the restriction lifting
    // This documents when and why the user chose to lift the restriction
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'restriction_lifted',                                       // Action type for filtering
        resource_type: 'data_processing',                                   // Type of resource affected
        resource_id: restriction_id,                                        // Which specific restriction was lifted
        details: {
          reason: reason || 'User requested to lift processing restrictions', // Reason for lifting
          lifted_by: 'user_request'                                         // Indicates user-initiated action
        },
        ip_address: clientIP,
        user_agent: userAgent
      });

    // Return success response confirming the restriction has been lifted
    return NextResponse.json({
      success: true,
      message: "Processing restrictions have been lifted successfully"
    });

  } catch (error: any) {
    // Handle any errors that occur while lifting restrictions
    console.error('Lift restriction error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Helper function to determine legal grounds for restriction based on user's reason
 * 
 * GDPR Article 18 specifies four specific grounds for restriction:
 * 1. Data accuracy is contested (while verification is pending)
 * 2. Processing is unlawful but user doesn't want erasure
 * 3. Data no longer needed but required for legal claims
 * 4. User has objected and verification of legitimate grounds is pending
 * 
 * @param reason - The user's stated reason for requesting restriction
 * @returns Array of applicable legal grounds
 */
function getRestrictionGrounds(reason: string): string[] {
  const grounds = [];
  
  // Check if user is contesting data accuracy (GDPR Article 18(1)(a))
  if (reason.toLowerCase().includes('inaccurate') || reason.toLowerCase().includes('incorrect')) {
    grounds.push('data_accuracy_contested');
  }
  
  // Check if user claims processing is unlawful (GDPR Article 18(1)(b))
  if (reason.toLowerCase().includes('unlawful') || reason.toLowerCase().includes('illegal')) {
    grounds.push('unlawful_processing');
  }
  
  // Check if user needs data for legal claims (GDPR Article 18(1)(c))
  if (reason.toLowerCase().includes('legal') || reason.toLowerCase().includes('court')) {
    grounds.push('legal_claims');
  }
  
  // Check if user has objected and verification is pending (GDPR Article 18(1)(d))
  if (reason.toLowerCase().includes('object') || reason.toLowerCase().includes('legitimate')) {
    grounds.push('objection_pending');
  }
  
  // Return identified grounds, or 'user_request' as fallback
  return grounds.length > 0 ? grounds : ['user_request'];
}
