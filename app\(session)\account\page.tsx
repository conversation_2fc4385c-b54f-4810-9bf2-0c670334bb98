"use client";

/**
 * Account Management Page
 * 
 * This comprehensive account management interface provides users with:
 * 
 * Core Features:
 * - Profile information management (name, email, password)
 * - Account security settings and session management
 * - Privacy and consent preference overview
 * - Purchase history and subscription management
 * - Account deletion with GDPR compliance
 * 
 * Security Features:
 * - Password change with current password verification
 * - Email change with verification process
 * - Session management and sign-out functionality
 * - Secure account deletion with confirmation
 * 
 * Privacy Features:
 * - Consent management integration
 * - GDPR rights access
 * - Data deletion preview and options
 * - Comprehensive audit trail
 * 
 * UX Features:
 * - Inline editing with smooth animations
 * - Real-time validation and feedback
 * - Progressive disclosure for dangerous actions
 * - Comprehensive error handling and user feedback
 */

import { useState } from "react";
import { useRouter } from "next/navigation";
import { authClient } from "@/lib/auth-client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TerminalButton } from "@/components/ui/terminal-button";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Purchases } from "./purchases";
import { ConsentSummary } from "@/components/consent/consent-summary";
import { AccountCookieSettings } from "@/components/account/cookie-settings";
import { QuickCookieToggle } from "@/components/account/quick-cookie-toggle";
import { 
  User,           // For user profile information
  Mail,           // For email-related functionality
  Calendar,       // For date/time information
  Shield,         // For security and privacy features
  LogOut,         // For sign-out functionality
  Loader2,        // For loading states
  Edit2,          // For edit mode toggles
  Save,           // For save actions
  X,              // For cancel actions
  Eye,            // For password visibility toggle
  EyeOff,         // For password visibility toggle
  Trash2,         // For deletion actions
  AlertTriangle   // For warnings and dangerous actions
} from "lucide-react";
import { ModernDashboardLayout } from "@/components/dashboard/modern-layout";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";

export default function AccountPage() {
  const router = useRouter();
  
  // Sign-out state management
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [signOutError, setSignOutError] = useState<string | null>(null);

  // Get user session data with real-time updates
  const { data: session, isPending, error, refetch } = authClient.useSession();

  // Edit mode states - control which fields are currently being edited
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingEmail, setIsEditingEmail] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // Form data states - store temporary values during editing
  const [editName, setEditName] = useState("");                    // Temporary name during editing
  const [editEmail, setEditEmail] = useState("");                  // Temporary email during editing
  const [passwordForm, setPasswordForm] = useState({               // Password change form data
    currentPassword: "",                                           // Current password for verification
    newPassword: "",                                               // New password
    confirmPassword: ""                                            // Confirmation of new password
  });

  // Loading states for different operations
  const [isUpdatingName, setIsUpdatingName] = useState(false);
  const [isUpdatingEmail, setIsUpdatingEmail] = useState(false);
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);

  // Password visibility toggles for better UX
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Account deletion states - complex flow with multiple steps
  const [showDeleteOptions, setShowDeleteOptions] = useState(false);        // Show deletion options
  const [hardDeleteConfirmation, setHardDeleteConfirmation] = useState(""); // Confirmation text input
  const [isDeleting, setIsDeleting] = useState(false);                      // Deletion in progress
  const [deletePassword, setDeletePassword] = useState("");                 // Password for deletion confirmation
  const [showDeletePassword, setShowDeletePassword] = useState(false);      // Password visibility toggle
  const [deletionInfo, setDeletionInfo] = useState<any>(null);              // Preview of data to be deleted
  const [showDeletionPreview, setShowDeletionPreview] = useState(false);    // Show deletion preview

  /**
   * Handle user sign-out with proper cleanup and error handling
   * 
   * This function manages the complete sign-out process including:
   * - Setting loading states for UI feedback
   * - Calling the authentication client to end the session
   * - Redirecting to home page on success
   * - Handling and displaying errors appropriately
   * - Cleaning up local state
   */
  const handleSignOut = async () => {
    setIsSigningOut(true);
    setSignOutError(null);

    try {
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            // Redirect to home page after successful sign out
            // This ensures users don't remain on authenticated pages
            router.push("/");
          },
          onError: (error) => {
            console.error("Sign out error:", error);
            setSignOutError("Failed to sign out. Please try again.");
            setIsSigningOut(false);
          }
        }
      });
    } catch (error) {
      // Handle any unexpected errors during sign-out
      console.error("Sign out error:", error);
      setSignOutError("Failed to sign out. Please try again.");
      setIsSigningOut(false);
    }
  };

  /**
   * Format date strings for user-friendly display
   * 
   * This utility function converts ISO date strings into readable format
   * with proper error handling for invalid dates.
   * 
   * @param dateString - ISO date string to format
   * @returns Formatted date string or "Unknown" if invalid
   */
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit"
      });
    } catch {
      // Return fallback for invalid dates
      return "Unknown";
    }
  };

  /**
   * Handle name update with validation and error handling
   * 
   * This function manages the complete name update process including:
   * - Client-side validation (non-empty, trimmed)
   * - API call to update user profile
   * - UI state management (loading, success, error)
   * - Session data refresh to reflect changes
   * - User feedback through toast notifications
   */
  const handleUpdateName = async () => {
    // Validate input before making API call
    if (!editName.trim()) {
      toast.error("Name cannot be empty");
      return;
    }

    setIsUpdatingName(true);
    try {
      // Update user profile through authentication client
      await authClient.updateUser({
        name: editName.trim()                    // Trim whitespace for clean data
      });

      toast.success("Name updated successfully");
      setIsEditingName(false);                   // Exit edit mode
      refetch();                                 // Refresh session data to show updated name
    } catch (error: any) {
      // Show specific error message or generic fallback
      toast.error(error.message || "Failed to update name");
    } finally {
      setIsUpdatingName(false);                  // Always clear loading state
    }
  };

  /**
   * Handle email update with comprehensive validation and security measures
   * 
   * This function manages the secure email change process including:
   * - Client-side validation (format, non-empty)
   * - Verification email sent to current address (security measure)
   * - UI state management and user feedback
   * - Proper error handling for various failure scenarios
   * 
   * Security Note: Email changes require verification to prevent account takeover
   */
  const handleUpdateEmail = async () => {
    // Validate email is not empty
    if (!editEmail.trim()) {
      toast.error("Email cannot be empty");
      return;
    }

    // Validate email format using regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(editEmail)) {
      toast.error("Please enter a valid email address");
      return;
    }

    setIsUpdatingEmail(true);
    try {
      // Initiate email change process (sends verification email)
      await authClient.changeEmail({
        newEmail: editEmail.trim(),              // New email address
        callbackURL: "/account"                  // Where to redirect after verification
      });

      // Inform user about verification process
      toast.success("Email change verification sent. Please check your current email.");
      setIsEditingEmail(false);                  // Exit edit mode
    } catch (error: any) {
      // Handle various error scenarios (email taken, invalid, etc.)
      toast.error(error.message || "Failed to update email");
    } finally {
      setIsUpdatingEmail(false);                 // Always clear loading state
    }
  };

  /**
   * Handle password change with comprehensive validation and security measures
   * 
   * This function manages the secure password change process including:
   * - Multi-step validation (current password, new password, confirmation)
   * - Security requirements enforcement (minimum length)
   * - Session management (revoke other sessions for security)
   * - Form cleanup and state management
   * - Comprehensive error handling and user feedback
   * 
   * Security Features:
   * - Requires current password verification
   * - Enforces minimum password length
   * - Revokes other sessions to prevent unauthorized access
   * - Clears form data after successful change
   */
  const handleChangePassword = async () => {
    // Validate current password is provided (required for security)
    if (!passwordForm.currentPassword) {
      toast.error("Current password is required");
      return;
    }

    // Validate new password is provided
    if (!passwordForm.newPassword) {
      toast.error("New password is required");
      return;
    }

    // Enforce minimum password length for security
    if (passwordForm.newPassword.length < 8) {
      toast.error("New password must be at least 8 characters long");
      return;
    }

    // Validate password confirmation matches
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast.error("New passwords do not match");
      return;
    }

    setIsUpdatingPassword(true);
    try {
      // Change password with security measures
      await authClient.changePassword({
        currentPassword: passwordForm.currentPassword,  // Verify current password
        newPassword: passwordForm.newPassword,          // Set new password
        revokeOtherSessions: true                       // Sign out other devices for security
      });

      toast.success("Password changed successfully");
      setIsChangingPassword(false);                     // Exit password change mode
      
      // Clear sensitive form data for security
      setPasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      });
    } catch (error: any) {
      // Handle various error scenarios (wrong current password, etc.)
      toast.error(error.message || "Failed to change password");
    } finally {
      setIsUpdatingPassword(false);                     // Always clear loading state
    }
  };

  // Helper functions
  const startEditingName = () => {
    setEditName(user?.name || "");
    setIsEditingName(true);
  };

  const startEditingEmail = () => {
    setEditEmail(user?.email || "");
    setIsEditingEmail(true);
  };

  const cancelEditingName = () => {
    setIsEditingName(false);
    setEditName("");
  };

  const cancelEditingEmail = () => {
    setIsEditingEmail(false);
    setEditEmail("");
  };

  const cancelChangingPassword = () => {
    setIsChangingPassword(false);
    setPasswordForm({
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    });
  };

  // Delete account functions

  const handleHardDelete = async () => {
    if (hardDeleteConfirmation !== "DELETE FOREVER") {
      toast.error('Please type "DELETE FOREVER" to confirm');
      return;
    }

    if (!deletePassword) {
      toast.error("Password is required for permanent deletion");
      return;
    }

    setIsDeleting(true);
    try {
      // Use our custom deletion API that properly cleans up all data
      const response = await fetch('/api/account/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          password: deletePassword,
          confirmation: hardDeleteConfirmation
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete account');
      }

      toast.success("Account and all data permanently deleted");

      // Sign out and redirect to home page
      setTimeout(() => {
        authClient.signOut({
          fetchOptions: {
            onSuccess: () => router.push("/")
          }
        });
      }, 2000);

    } catch (error: any) {
      toast.error(error.message || "Failed to delete account");
    } finally {
      setIsDeleting(false);
    }
  };

  const fetchDeletionInfo = async () => {
    try {
      const response = await fetch('/api/account/delete');
      if (response.ok) {
        const info = await response.json();
        setDeletionInfo(info);
        setShowDeletionPreview(true);
      }
    } catch (error) {
      console.error('Failed to fetch deletion info:', error);
    }
  };

  const resetDeleteForm = () => {
    setShowDeleteOptions(false);
    setHardDeleteConfirmation("");
    setDeletePassword("");
    setShowDeletePassword(false);
    setShowDeletionPreview(false);
    setDeletionInfo(null);
  };

  if (isPending) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-mono text-neutral-200">Account</h1>
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3 text-neutral-400 font-mono">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>Loading account information...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-mono text-neutral-200">Account</h1>
        <Card className="border-red-800/50 bg-black/40 backdrop-blur">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 text-red-400 font-mono">
              <Shield className="w-5 h-5" />
              <span>Error loading account: {error.message}</span>
            </div>
            <TerminalButton
              onClick={() => refetch()}
              className="mt-4"
              prompt="$"
              command="retry"
              path="/account"
            >
              Retry
            </TerminalButton>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!session?.user) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-mono text-neutral-200">Account</h1>
        <Card className="border-yellow-800/50 bg-black/40 backdrop-blur">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 text-yellow-400 font-mono">
              <Shield className="w-5 h-5" />
              <span>No active session found</span>
            </div>
            <TerminalButton
              onClick={() => router.push("/signin")}
              className="mt-4"
              prompt="$"
              command="signin"
              path="/auth"
            >
              Sign In
            </TerminalButton>
          </CardContent>
        </Card>
      </div>
    );
  }

  const user = session.user;

  return (
    <ModernDashboardLayout
      title="Account"
      subtitle="Manage your profile and account settings"
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Active Session
          </Badge>
        </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* User Profile Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
              <User className="w-5 h-5 text-[#ffbe98]" />
              Profile Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Name */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm text-neutral-600 dark:text-neutral-400">Name</label>
                {!isEditingName && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={startEditingName}
                    className="h-8 px-2 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
                  >
                    <Edit2 className="w-3 h-3" />
                  </Button>
                )}
              </div>

              <AnimatePresence mode="wait">
                {isEditingName ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="space-y-3"
                  >
                    <div className="flex items-center gap-2">
                      <div className="flex-1 relative">
                        <User className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                        <Input
                          value={editName}
                          onChange={(e) => setEditName(e.target.value)}
                          placeholder="Enter your name"
                          className="pl-10"
                          disabled={isUpdatingName}
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={handleUpdateName}
                        disabled={isUpdatingName || !editName.trim()}
                        className="bg-[#ffbe98] hover:bg-[#ffbe98]/90 text-white"
                      >
                        {isUpdatingName ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Save className="w-3 h-3" />
                        )}
                        {isUpdatingName ? "Saving..." : "Save"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={cancelEditingName}
                        disabled={isUpdatingName}
                      >
                        <X className="w-3 h-3" />
                        Cancel
                      </Button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="flex items-center gap-3 p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700"
                  >
                    <User className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                    <span className="text-neutral-900 dark:text-neutral-200">
                      {user.name || "Not provided"}
                    </span>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Email */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm text-neutral-600 dark:text-neutral-400">Email Address</label>
                {!isEditingEmail && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={startEditingEmail}
                    className="h-8 px-2 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
                  >
                    <Edit2 className="w-3 h-3" />
                  </Button>
                )}
              </div>

              <AnimatePresence mode="wait">
                {isEditingEmail ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="space-y-3"
                  >
                    <div className="flex items-center gap-2">
                      <div className="flex-1 relative">
                        <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                        <Input
                          type="email"
                          value={editEmail}
                          onChange={(e) => setEditEmail(e.target.value)}
                          placeholder="Enter your email"
                          className="pl-10"
                          disabled={isUpdatingEmail}
                        />
                      </div>
                    </div>
                    <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        A verification email will be sent to your current email address to confirm this change.
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={handleUpdateEmail}
                        disabled={isUpdatingEmail || !editEmail.trim()}
                        className="bg-[#ffbe98] hover:bg-[#ffbe98]/90 text-white"
                      >
                        {isUpdatingEmail ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Save className="w-3 h-3" />
                        )}
                        {isUpdatingEmail ? "Sending..." : "Send Verification"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={cancelEditingEmail}
                        disabled={isUpdatingEmail}
                      >
                        <X className="w-3 h-3" />
                        Cancel
                      </Button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="flex items-center gap-3 p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700"
                  >
                    <Mail className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                    <span className="text-neutral-900 dark:text-neutral-200">{user.email}</span>
                    {user.emailVerified && (
                      <Badge variant="default" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Verified
                      </Badge>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Change Password */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm text-neutral-600 dark:text-neutral-400">Password</label>
                {!isChangingPassword && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsChangingPassword(true)}
                    className="h-8 px-2 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
                  >
                    <Edit2 className="w-3 h-3" />
                  </Button>
                )}
              </div>

              <AnimatePresence mode="wait">
                {isChangingPassword ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="space-y-3"
                  >
                    {/* Current Password */}
                    <div className="relative">
                      <Shield className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                      <Input
                        type={showCurrentPassword ? "text" : "password"}
                        value={passwordForm.currentPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                        placeholder="Current password"
                        className="pl-10 pr-10"
                        disabled={isUpdatingPassword}
                      />
                      <button
                        type="button"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300"
                        disabled={isUpdatingPassword}
                      >
                        {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>

                    {/* New Password */}
                    <div className="relative">
                      <Shield className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                      <Input
                        type={showNewPassword ? "text" : "password"}
                        value={passwordForm.newPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                        placeholder="New password (min. 8 characters)"
                        className="pl-10 pr-10"
                        disabled={isUpdatingPassword}
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300"
                        disabled={isUpdatingPassword}
                      >
                        {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>

                    {/* Confirm Password */}
                    <div className="relative">
                      <Shield className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                      <Input
                        type={showConfirmPassword ? "text" : "password"}
                        value={passwordForm.confirmPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        placeholder="Confirm new password"
                        className="pl-10 pr-10"
                        disabled={isUpdatingPassword}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300"
                        disabled={isUpdatingPassword}
                      >
                        {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>

                    <div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
                      <p className="text-sm text-amber-700 dark:text-amber-300">
                        Changing your password will sign you out of all other devices for security.
                      </p>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={handleChangePassword}
                        disabled={isUpdatingPassword || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
                        className="bg-[#ffbe98] hover:bg-[#ffbe98]/90 text-white"
                      >
                        {isUpdatingPassword ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Save className="w-3 h-3" />
                        )}
                        {isUpdatingPassword ? "Changing..." : "Change Password"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={cancelChangingPassword}
                        disabled={isUpdatingPassword}
                      >
                        <X className="w-3 h-3" />
                        Cancel
                      </Button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="flex items-center gap-3 p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700"
                  >
                    <Shield className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                    <span className="text-neutral-900 dark:text-neutral-200">••••••••</span>
                    <Badge variant="default" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      Protected
                    </Badge>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Account Created */}
            <div className="space-y-2">
              <label className="text-sm text-neutral-600 dark:text-neutral-400">Account Created</label>
              <div className="flex items-center gap-3 p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700">
                <Calendar className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                <span className="text-neutral-900 dark:text-neutral-200">
                  {user.createdAt ? formatDate(user.createdAt.toISOString()) : "Unknown"}
                </span>
              </div>
            </div>


          </CardContent>
        </Card>

        {/* Account Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
              <Shield className="w-5 h-5 text-[#ffbe98]" />
              Account Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Manage your account security and session
              </p>

              {/* Quick Cookie Settings */}
              <div className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-900">
                <h4 className="text-sm font-medium mb-2 text-neutral-900 dark:text-neutral-200">
                  Quick Cookie Settings
                </h4>
                <QuickCookieToggle compact={true} />
              </div>

              {/* Sign Out Button */}
              <div className="space-y-3">
                <TerminalButton
                  onClick={handleSignOut}
                  disabled={isSigningOut}
                  className="w-full justify-center bg-red-950/20 border-red-800/50 hover:bg-red-950/30 hover:border-red-700/50"
                  prompt="$"
                  command={isSigningOut ? "signing-out..." : "signout"}
                  path="/auth"
                >
                  <div className="flex items-center gap-2">
                    {isSigningOut ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <LogOut className="w-4 h-4" />
                    )}
                    {isSigningOut ? "Signing out..." : "Sign Out"}
                  </div>
                </TerminalButton>

                {signOutError && (
                  <div className="text-red-400 font-mono text-xs p-2 rounded bg-red-950/20 border border-red-800/50">
                    {signOutError}
                  </div>
                )}

                <p className="text-xs font-mono text-neutral-500">
                  This will end your current session and redirect you to the home page.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Delete Account Section */}
        <Card className="border-red-200 dark:border-red-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-red-600 dark:text-red-400">
              <Shield className="w-5 h-5" />
              Danger Zone
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              These actions will affect your account permanently. Please read carefully before proceeding.
            </p>

            {/* Delete Options Toggle */}
            {!showDeleteOptions ? (
              <Button
                variant="outline"
                onClick={() => setShowDeleteOptions(true)}
                className="w-full border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950/20"
              >
                Manage Account Deletion
              </Button>
            ) : (
              <div className="space-y-6">
                {/* Hard Delete Option */}
                <div className="p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950/20">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                      <div className="flex-1">
                        <h4 className="font-medium text-red-800 dark:text-red-200">
                          Delete Account Permanently
                        </h4>
                        <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                          Permanently delete your account and all associated data. This action cannot be undone.
                        </p>
                        <ul className="text-xs text-red-600 dark:text-red-400 mt-2 space-y-1 ml-4">
                          <li>• All data is permanently deleted</li>
                          <li>• Cannot be recovered</li>
                          <li>• Subscriptions are cancelled</li>
                          <li>• Purchase history is removed</li>
                        </ul>

                        {!showDeletionPreview && (
                          <Button
                            onClick={fetchDeletionInfo}
                            variant="outline"
                            size="sm"
                            className="mt-3 border-red-300 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-950/30"
                          >
                            Preview What Will Be Deleted
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Deletion Preview */}
                    {showDeletionPreview && deletionInfo && (
                      <div className="mt-4 p-3 bg-red-100 dark:bg-red-950/40 rounded border border-red-200 dark:border-red-800">
                        <h5 className="font-medium text-red-800 dark:text-red-200 mb-2">
                          Data to be permanently deleted:
                        </h5>
                        <div className="text-xs text-red-700 dark:text-red-300 space-y-1">
                          <div>• Account: {deletionInfo.user.email}</div>
                          <div>• Subscriptions: {deletionInfo.dataToDelete.subscriptions} total ({deletionInfo.dataToDelete.activeSubscriptions} active)</div>
                          <div>• Sessions: {deletionInfo.dataToDelete.sessions}</div>
                          <div>• OAuth Accounts: {deletionInfo.dataToDelete.oauthAccounts}</div>
                        </div>
                        <p className="text-xs text-red-600 dark:text-red-400 mt-2 font-medium">
                          ⚠️ {deletionInfo.warning}
                        </p>
                      </div>
                    )}

                    <div className="space-y-3">
                      <Input
                        placeholder='Type "DELETE FOREVER" to confirm'
                        value={hardDeleteConfirmation}
                        onChange={(e) => setHardDeleteConfirmation(e.target.value)}
                        disabled={isDeleting}
                        className="border-red-300 dark:border-red-700"
                      />
                      <div className="relative">
                        <Input
                          type={showDeletePassword ? "text" : "password"}
                          placeholder="Enter your password"
                          value={deletePassword}
                          onChange={(e) => setDeletePassword(e.target.value)}
                          disabled={isDeleting}
                          className="border-red-300 dark:border-red-700 pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowDeletePassword(!showDeletePassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300"
                          disabled={isDeleting}
                        >
                          {showDeletePassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                      <Button
                        onClick={handleHardDelete}
                        disabled={isDeleting || hardDeleteConfirmation !== "DELETE FOREVER" || !deletePassword}
                        className="w-full bg-red-600 hover:bg-red-700 text-white"
                      >
                        {isDeleting ? (
                          <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        ) : null}
                        {isDeleting ? "Deleting..." : "Delete Account Forever"}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Cancel Button */}
                <Button
                  variant="outline"
                  onClick={resetDeleteForm}
                  disabled={isDeleting}
                  className="w-full"
                >
                  Cancel
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Privacy & Consent Section */}
      <div className="space-y-4">
        <h2 className="text-xl text-neutral-900 dark:text-neutral-200">Privacy & Consent</h2>
        <div className="grid gap-4 lg:grid-cols-2">
          <ConsentSummary />
          <AccountCookieSettings />
        </div>
      </div>

        {/* Purchases Section */}
        <div className="space-y-4">
          <h2 className="text-xl text-neutral-900 dark:text-neutral-200">Purchase History</h2>
          <Purchases />
        </div>
      </div>
    </ModernDashboardLayout>
  );
}