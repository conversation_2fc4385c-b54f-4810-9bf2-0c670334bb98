"use client";

import { ModernDashboardLayout } from "@/components/dashboard/modern-layout";
import { motion } from "framer-motion";
import {
  TrendingUp,
  Users,
  User,
  CreditCard,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  Calendar,
  Filter,
  Download
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { authClient } from "@/lib/auth-client";

// Mock data for demonstration
const stats = [
  {
    title: "Total Revenue",
    value: "$45,231.89",
    change: "+20.1%",
    trend: "up",
    icon: CreditCard,
    color: "text-green-600"
  },
  {
    title: "Subscriptions",
    value: "2,350",
    change: "+180.1%",
    trend: "up",
    icon: Users,
    color: "text-blue-600"
  },
  {
    title: "Monthly Revenue",
    value: "$12,234",
    change: "+19%",
    trend: "up",
    icon: CreditCard,
    color: "text-purple-600"
  },
  {
    title: "Growth Rate",
    value: "23.5%",
    change: "+5.2%",
    trend: "up",
    icon: TrendingUp,
    color: "text-orange-600"
  }
];

const recentActivity = [
  {
    id: 1,
    type: "subscription",
    user: "John Doe",
    action: "subscribed to Pro Plan",
    amount: "$29.99",
    time: "2 minutes ago"
  },
  {
    id: 2,
    type: "purchase",
    user: "Sarah Wilson",
    action: "purchased Premium Template",
    amount: "$49.99",
    time: "5 minutes ago"
  },
  {
    id: 3,
    type: "subscription",
    user: "Mike Johnson",
    action: "upgraded to Enterprise",
    amount: "$99.99",
    time: "10 minutes ago"
  },
  {
    id: 4,
    type: "purchase",
    user: "Emily Brown",
    action: "purchased Starter Pack",
    amount: "$19.99",
    time: "15 minutes ago"
  }
];

export default function DashboardPage() {
  const { data: session } = authClient.useSession();
  const user = session?.user;

  return (
    <ModernDashboardLayout
      title="Dashboard"
      subtitle={`Welcome back, ${user?.name || user?.email?.split('@')[0] || 'User'}!`}
    >
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="relative overflow-hidden">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                      {stat.title}
                    </CardTitle>
                    <Icon className={`w-4 h-4 ${stat.color}`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-neutral-900 dark:text-white">
                      {stat.value}
                    </div>
                    <div className="flex items-center text-xs text-neutral-600 dark:text-neutral-400 mt-1">
                      {stat.trend === "up" ? (
                        <ArrowUpRight className="w-3 h-3 text-green-600 mr-1" />
                      ) : (
                        <ArrowDownRight className="w-3 h-3 text-red-600 mr-1" />
                      )}
                      <span className={stat.trend === "up" ? "text-green-600" : "text-red-600"}>
                        {stat.change}
                      </span>
                      <span className="ml-1">from last month</span>
                    </div>
                  </CardContent>

                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-neutral-100/5 dark:to-neutral-800/5 pointer-events-none" />
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Charts and Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Revenue Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.5 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg font-semibold text-neutral-900 dark:text-white">
                      Revenue Overview
                    </CardTitle>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
                      Monthly revenue for the last 6 months
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Calendar className="w-4 h-4 mr-2" />
                      Last 6 months
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Placeholder for chart */}
                <div className="h-64 bg-gradient-to-br from-[#ffbe98]/10 to-[#ff9a56]/10 rounded-lg flex items-center justify-center border border-dashed border-neutral-300 dark:border-neutral-700">
                  <div className="text-center">
                    <TrendingUp className="w-12 h-12 text-neutral-400 mx-auto mb-2" />
                    <p className="text-neutral-600 dark:text-neutral-400">Chart Component</p>
                    <p className="text-sm text-neutral-500 dark:text-neutral-500">Revenue chart will be displayed here</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.6 }}
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-neutral-900 dark:text-white">
                    Recent Activity
                  </CardTitle>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                    className="flex items-start gap-3 p-3 rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-800/50 transition-colors"
                  >
                    <div className="w-2 h-2 bg-[#ffbe98] rounded-full mt-2 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-neutral-900 dark:text-white">
                        {activity.user}
                      </p>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        {activity.action}
                      </p>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-sm font-medium text-green-600">
                          {activity.amount}
                        </span>
                        <span className="text-xs text-neutral-500 dark:text-neutral-500">
                          {activity.time}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}

                <Button variant="ghost" className="w-full mt-4 text-[#ffbe98] hover:text-[#ffbe98]/80">
                  View all activity
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.8 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-neutral-900 dark:text-white">
                Quick Actions
              </CardTitle>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Common tasks and shortcuts
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button className="h-auto p-4 flex flex-col items-center gap-2 bg-gradient-to-br from-[#ffbe98] to-[#ff9a56] hover:from-[#ffbe98]/90 hover:to-[#ff9a56]/90">
                  <CreditCard className="w-6 h-6" />
                  <span>Manage Billing</span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                  <User className="w-6 h-6" />
                  <span>Account Settings</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </ModernDashboardLayout>
  );
}
