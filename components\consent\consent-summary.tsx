"use client";

/**
 * Consent Summary Component
 * 
 * This component provides a compact overview of the user's current consent
 * preferences for display in dashboards and account pages. It shows:
 * 
 * Key Features:
 * - Quick overview of GDPR consent status
 * - Cookie preference summary
 * - Last updated timestamp
 * - Quick action buttons for management
 * - Real-time status indicators
 * 
 * Design Philosophy:
 * - Minimal and informative
 * - Quick access to full consent management
 * - Clear visual indicators for consent states
 * - Privacy-focused default assumptions
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Shield,         // For privacy and security indicators
  Cookie,         // For cookie-related preferences
  Mail,           // For marketing email preferences
  BarChart3,      // For analytics preferences
  Target,         // For marketing/advertising preferences
  CheckCircle,    // For active/granted consent states
  XCircle,        // For inactive/withdrawn consent states
  Settings,       // For management/configuration actions
  Loader2         // For loading states
} from 'lucide-react';
import Link from 'next/link';

/**
 * Interface representing summarized consent data for dashboard display
 * This is a simplified version of the full consent data structure,
 * containing only the information needed for the summary view
 */
interface ConsentSummaryData {
  gdprConsents: {
    marketing_emails: boolean;    // Whether user consented to marketing emails
  };
  cookieConsents: {
    analytics_cookies: boolean;   // Whether user allows analytics cookies
    marketing_cookies: boolean;   // Whether user allows marketing cookies
  };
  lastUpdated: string | null;     // When consent preferences were last modified
}

export function ConsentSummary() {
  // State for storing the summarized consent data
  const [consentData, setConsentData] = useState<ConsentSummaryData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load consent summary when component mounts
  useEffect(() => {
    fetchConsentSummary();
  }, []);

  /**
   * Fetch and process consent data for summary display
   * 
   * This function retrieves consent data from both GDPR and cookie APIs,
   * then processes it into a simplified format suitable for dashboard display.
   * It handles errors gracefully by failing silently (since this is a summary widget).
   */
  const fetchConsentSummary = async () => {
    try {
      // Fetch both consent types concurrently for better performance
      const [gdprResponse, cookieResponse] = await Promise.all([
        fetch('/api/gdpr/consent'),           // Get GDPR consent data
        fetch('/api/gdpr/cookie-consent')     // Get cookie preference data
      ]);

      // Only process data if both requests succeeded
      // This ensures we have complete information for the summary
      if (gdprResponse.ok && cookieResponse.ok) {
        const [gdprData, cookieData] = await Promise.all([
          gdprResponse.json(),
          cookieResponse.json()
        ]);

        // Process and normalize the data for summary display
        setConsentData({
          gdprConsents: {
            // Extract marketing email consent status with privacy-first default
            marketing_emails: gdprData.consents?.marketing_emails?.given || false
          },
          cookieConsents: {
            // Extract cookie preferences with privacy-first defaults
            analytics_cookies: cookieData.consent?.analytics_cookies || false,
            marketing_cookies: cookieData.consent?.marketing_cookies || false
          },
          // Determine the most recent update timestamp from either source
          lastUpdated: cookieData.consent?.consent_date || gdprData.consents?.marketing_emails?.date || null
        });
      }
    } catch (error) {
      // Log error for debugging but don't show user error (this is a summary widget)
      console.error('Failed to fetch consent summary:', error);
    } finally {
      // Always stop loading indicator
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span className="ml-2">Loading consent status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!consentData) {
    return null;
  }

  /**
   * Generate status badge for consent state in summary view
   * 
   * This helper function creates compact visual indicators for consent states
   * optimized for the summary display format.
   * 
   * @param granted - Whether the consent has been granted
   * @returns JSX badge component with appropriate styling and terminology
   */
  const getStatusBadge = (granted: boolean) => {
    return granted ? (
      <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
        <CheckCircle className="w-3 h-3 mr-1" />
        Active                                    // "Active" is more user-friendly than "Granted"
      </Badge>
    ) : (
      <Badge variant="outline" className="text-gray-600">
        <XCircle className="w-3 h-3 mr-1" />
        Disabled                                  // "Disabled" is clearer than "Withdrawn" for summary
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-500" />
            Privacy & Consent Status
          </div>
          <Link href="/privacy/consent">
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Manage
            </Button>
          </Link>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* GDPR Consents */}
        <div>
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Shield className="w-4 h-4" />
            Data Processing
          </h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Mail className="w-3 h-3 text-muted-foreground" />
                <span className="text-sm">Marketing Emails</span>
              </div>
              {getStatusBadge(consentData.gdprConsents.marketing_emails)}
            </div>
          </div>
        </div>

        {/* Cookie Consents */}
        <div>
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Cookie className="w-4 h-4" />
            Cookie Preferences
          </h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <BarChart3 className="w-3 h-3 text-muted-foreground" />
                <span className="text-sm">Analytics Cookies</span>
              </div>
              {getStatusBadge(consentData.cookieConsents.analytics_cookies)}
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Target className="w-3 h-3 text-muted-foreground" />
                <span className="text-sm">Marketing Cookies</span>
              </div>
              {getStatusBadge(consentData.cookieConsents.marketing_cookies)}
            </div>
          </div>
        </div>

        {/* Last Updated */}
        {consentData.lastUpdated && (
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground">
              Last updated: {new Date(consentData.lastUpdated).toLocaleDateString()}
            </p>
          </div>
        )}

        {/* Quick Actions */}
        <div className="pt-2 border-t">
          <div className="flex gap-2">
            <Link href="/privacy/consent" className="flex-1">
              <Button variant="outline" size="sm" className="w-full">
                Update Preferences
              </Button>
            </Link>
            <Link href="/gdpr" className="flex-1">
              <Button variant="outline" size="sm" className="w-full">
                GDPR Rights
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
