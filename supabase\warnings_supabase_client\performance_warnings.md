| name                         | title                        | level | facing   | categories      | description                                                                                                                                                                                                                                                 | detail                                                                                                                                                                                                                                     | remediation                                                                                      | metadata                                                       | cache_key                                                                    |
| ---------------------------- | ---------------------------- | ----- | -------- | --------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------ | -------------------------------------------------------------- | ---------------------------------------------------------------------------- |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.accounts\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage accounts","Users and service can view accounts"}\`                                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"accounts","type":"table","schema":"public"}           | multiple_permissive_policies_public_accounts_anon_SELECT                     |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.accounts\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage accounts","Users and service can view accounts"}\`                               | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"accounts","type":"table","schema":"public"}           | multiple_permissive_policies_public_accounts_authenticated_SELECT            |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.accounts\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage accounts","Users and service can view accounts"}\`                               | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"accounts","type":"table","schema":"public"}           | multiple_permissive_policies_public_accounts_authenticator_SELECT            |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.accounts\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage accounts","Users and service can view accounts"}\`                              | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"accounts","type":"table","schema":"public"}           | multiple_permissive_policies_public_accounts_dashboard_user_SELECT           |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.audit_logs\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage audit logs","Users and service can view audit logs"}\`                                  | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"audit_logs","type":"table","schema":"public"}         | multiple_permissive_policies_public_audit_logs_anon_SELECT                   |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.audit_logs\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage audit logs","Users and service can view audit logs"}\`                         | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"audit_logs","type":"table","schema":"public"}         | multiple_permissive_policies_public_audit_logs_authenticated_SELECT          |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.audit_logs\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage audit logs","Users and service can view audit logs"}\`                         | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"audit_logs","type":"table","schema":"public"}         | multiple_permissive_policies_public_audit_logs_authenticator_SELECT          |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.audit_logs\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage audit logs","Users and service can view audit logs"}\`                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"audit_logs","type":"table","schema":"public"}         | multiple_permissive_policies_public_audit_logs_dashboard_user_SELECT         |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.deletion_requests\` has multiple permissive policies for role \`anon\` for action \`INSERT\`. Policies include \`{"Service role can manage deletion requests","Users and service can insert deletion requests"}\`           | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"deletion_requests","type":"table","schema":"public"}  | multiple_permissive_policies_public_deletion_requests_anon_INSERT            |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.deletion_requests\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage deletion requests","Users and service can view deletion requests"}\`             | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"deletion_requests","type":"table","schema":"public"}  | multiple_permissive_policies_public_deletion_requests_anon_SELECT            |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.deletion_requests\` has multiple permissive policies for role \`authenticated\` for action \`INSERT\`. Policies include \`{"Service role can manage deletion requests","Users and service can insert deletion requests"}\`  | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"deletion_requests","type":"table","schema":"public"}  | multiple_permissive_policies_public_deletion_requests_authenticated_INSERT   |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.deletion_requests\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage deletion requests","Users and service can view deletion requests"}\`    | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"deletion_requests","type":"table","schema":"public"}  | multiple_permissive_policies_public_deletion_requests_authenticated_SELECT   |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.deletion_requests\` has multiple permissive policies for role \`authenticator\` for action \`INSERT\`. Policies include \`{"Service role can manage deletion requests","Users and service can insert deletion requests"}\`  | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"deletion_requests","type":"table","schema":"public"}  | multiple_permissive_policies_public_deletion_requests_authenticator_INSERT   |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.deletion_requests\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage deletion requests","Users and service can view deletion requests"}\`    | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"deletion_requests","type":"table","schema":"public"}  | multiple_permissive_policies_public_deletion_requests_authenticator_SELECT   |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.deletion_requests\` has multiple permissive policies for role \`dashboard_user\` for action \`INSERT\`. Policies include \`{"Service role can manage deletion requests","Users and service can insert deletion requests"}\` | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"deletion_requests","type":"table","schema":"public"}  | multiple_permissive_policies_public_deletion_requests_dashboard_user_INSERT  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.deletion_requests\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage deletion requests","Users and service can view deletion requests"}\`   | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"deletion_requests","type":"table","schema":"public"}  | multiple_permissive_policies_public_deletion_requests_dashboard_user_SELECT  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.one_time_purchases\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage purchases","Users and service can view purchases"}\`                            | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"one_time_purchases","type":"table","schema":"public"} | multiple_permissive_policies_public_one_time_purchases_anon_SELECT           |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.one_time_purchases\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage purchases","Users and service can view purchases"}\`                   | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"one_time_purchases","type":"table","schema":"public"} | multiple_permissive_policies_public_one_time_purchases_authenticated_SELECT  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.one_time_purchases\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage purchases","Users and service can view purchases"}\`                   | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"one_time_purchases","type":"table","schema":"public"} | multiple_permissive_policies_public_one_time_purchases_authenticator_SELECT  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.one_time_purchases\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage purchases","Users and service can view purchases"}\`                  | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"one_time_purchases","type":"table","schema":"public"} | multiple_permissive_policies_public_one_time_purchases_dashboard_user_SELECT |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`anon\` for action \`DELETE\`. Policies include \`{"Service role can insert update sessions","Users and service can delete sessions"}\`                               | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_anon_DELETE                     |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can insert update sessions","Users and service can view sessions"}\`                                 | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_anon_SELECT                     |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`authenticated\` for action \`DELETE\`. Policies include \`{"Service role can insert update sessions","Users and service can delete sessions"}\`                      | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_authenticated_DELETE            |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can insert update sessions","Users and service can view sessions"}\`                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_authenticated_SELECT            |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`authenticator\` for action \`DELETE\`. Policies include \`{"Service role can insert update sessions","Users and service can delete sessions"}\`                      | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_authenticator_DELETE            |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can insert update sessions","Users and service can view sessions"}\`                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_authenticator_SELECT            |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`dashboard_user\` for action \`DELETE\`. Policies include \`{"Service role can insert update sessions","Users and service can delete sessions"}\`                     | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_dashboard_user_DELETE           |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can insert update sessions","Users and service can view sessions"}\`                       | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_dashboard_user_SELECT           |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.subscriptions\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage subscriptions","Users and service can view subscriptions"}\`                         | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"subscriptions","type":"table","schema":"public"}      | multiple_permissive_policies_public_subscriptions_anon_SELECT                |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.subscriptions\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage subscriptions","Users and service can view subscriptions"}\`                | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"subscriptions","type":"table","schema":"public"}      | multiple_permissive_policies_public_subscriptions_authenticated_SELECT       |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.subscriptions\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage subscriptions","Users and service can view subscriptions"}\`                | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"subscriptions","type":"table","schema":"public"}      | multiple_permissive_policies_public_subscriptions_authenticator_SELECT       |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.subscriptions\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage subscriptions","Users and service can view subscriptions"}\`               | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"subscriptions","type":"table","schema":"public"}      | multiple_permissive_policies_public_subscriptions_dashboard_user_SELECT      |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.user_consents\` has multiple permissive policies for role \`anon\` for action \`INSERT\`. Policies include \`{"Service role can manage consents","Users and service can insert consents"}\`                                 | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"user_consents","type":"table","schema":"public"}      | multiple_permissive_policies_public_user_consents_anon_INSERT                |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.user_consents\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage consents","Users and service can view consents"}\`                                   | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"user_consents","type":"table","schema":"public"}      | multiple_permissive_policies_public_user_consents_anon_SELECT                |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.user_consents\` has multiple permissive policies for role \`authenticated\` for action \`INSERT\`. Policies include \`{"Service role can manage consents","Users and service can insert consents"}\`                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"user_consents","type":"table","schema":"public"}      | multiple_permissive_policies_public_user_consents_authenticated_INSERT       |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.user_consents\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage consents","Users and service can view consents"}\`                          | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"user_consents","type":"table","schema":"public"}      | multiple_permissive_policies_public_user_consents_authenticated_SELECT       |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.user_consents\` has multiple permissive policies for role \`authenticator\` for action \`INSERT\`. Policies include \`{"Service role can manage consents","Users and service can insert consents"}\`                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"user_consents","type":"table","schema":"public"}      | multiple_permissive_policies_public_user_consents_authenticator_INSERT       |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.user_consents\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage consents","Users and service can view consents"}\`                          | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"user_consents","type":"table","schema":"public"}      | multiple_permissive_policies_public_user_consents_authenticator_SELECT       |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.user_consents\` has multiple permissive policies for role \`dashboard_user\` for action \`INSERT\`. Policies include \`{"Service role can manage consents","Users and service can insert consents"}\`                       | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"user_consents","type":"table","schema":"public"}      | multiple_permissive_policies_public_user_consents_dashboard_user_INSERT      |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.user_consents\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage consents","Users and service can view consents"}\`                         | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"user_consents","type":"table","schema":"public"}      | multiple_permissive_policies_public_user_consents_dashboard_user_SELECT      |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can insert delete users","Users and service can view users"}\`                                          | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_anon_SELECT                        |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`anon\` for action \`UPDATE\`. Policies include \`{"Service role can insert delete users","Users and service can update users"}\`                                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_anon_UPDATE                        |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can insert delete users","Users and service can view users"}\`                                 | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_authenticated_SELECT               |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`authenticated\` for action \`UPDATE\`. Policies include \`{"Service role can insert delete users","Users and service can update users"}\`                               | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_authenticated_UPDATE               |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can insert delete users","Users and service can view users"}\`                                 | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_authenticator_SELECT               |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`authenticator\` for action \`UPDATE\`. Policies include \`{"Service role can insert delete users","Users and service can update users"}\`                               | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_authenticator_UPDATE               |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can insert delete users","Users and service can view users"}\`                                | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_dashboard_user_SELECT              |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`dashboard_user\` for action \`UPDATE\`. Policies include \`{"Service role can insert delete users","Users and service can update users"}\`                              | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_dashboard_user_UPDATE              |