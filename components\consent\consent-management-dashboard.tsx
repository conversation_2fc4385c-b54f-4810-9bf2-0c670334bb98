"use client";

/**
 * Consent Management Dashboard Component
 * 
 * This component provides users with a comprehensive interface to manage their
 * GDPR consent preferences and cookie settings. It implements GDPR Article 7
 * requirements for consent management including:
 * 
 * Key Features:
 * - View current consent status for all data processing activities
 * - Toggle consent for optional processing (marketing, analytics, etc.)
 * - Maintain required consents (privacy policy, terms of service)
 * - View complete consent history for transparency
 * - Bulk withdrawal of all optional consents
 * - Real-time updates with proper error handling
 * - Comprehensive audit logging of all consent changes
 * 
 * GDPR Compliance:
 * - Article 7: Conditions for consent
 * - Article 21: Right to object
 * - Recital 32: Consent should be given by clear affirmative action
 * - Recital 42: Consent should not be bundled
 */

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Shield,         // For security and privacy indicators
  Cookie,         // For cookie-related settings
  Mail,           // For marketing email preferences
  BarChart3,      // For analytics preferences
  Target,         // For marketing/advertising preferences
  Clock,          // For timestamps and time-related info
  CheckCircle,    // For granted/active consent states
  XCircle,        // For withdrawn/inactive consent states
  Loader2,        // For loading states
  AlertTriangle,  // For warnings and important notices
  Info,           // For informational messages
  History         // For consent history functionality
} from 'lucide-react';
import { toast } from 'sonner';

/**
 * Interface representing GDPR consent data structure
 * Each consent type includes metadata for audit and transparency purposes
 */
interface ConsentData {
  privacy_policy?: {
    given: boolean;        // Whether consent was granted
    date: string;          // When consent was given/updated
    id: string;           // Unique identifier for audit trail
  };
  terms_of_service?: {
    given: boolean;        // Whether terms were accepted
    date: string;          // When terms were accepted/updated
    id: string;           // Unique identifier for audit trail
  };
  marketing_emails?: {
    given: boolean;        // Whether marketing consent was granted
    date: string;          // When marketing consent was given/withdrawn
    id: string;           // Unique identifier for audit trail
  };
}

/**
 * Interface representing cookie consent preferences
 * Follows the standard cookie categorization for GDPR compliance
 */
interface CookieConsentData {
  necessary_cookies: boolean;    // Always true - required for site functionality
  analytics_cookies: boolean;   // Optional - for usage analytics and improvements
  marketing_cookies: boolean;   // Optional - for advertising and marketing
  consent_date: string | null;  // When cookie preferences were last updated
}

/**
 * Combined interface representing the complete consent status
 * This aggregates both GDPR consents and cookie preferences
 */
interface ConsentStatus {
  gdprConsents: ConsentData;        // All GDPR-related consent data
  cookieConsents: CookieConsentData; // Cookie preference settings
  consentHistory: any[];            // Complete history of consent changes
}

export function ConsentManagementDashboard() {
  // Main state for storing the complete consent status from the server
  const [consentStatus, setConsentStatus] = useState<ConsentStatus | null>(null);
  
  // Loading and UI state management
  const [isLoading, setIsLoading] = useState(true);        // Initial data loading
  const [isUpdating, setIsUpdating] = useState(false);     // Update operations in progress
  const [showHistory, setShowHistory] = useState(false);   // Toggle for consent history display

  // Local state for GDPR consent toggles
  // This allows users to make changes before committing them to the server
  const [localGdprConsents, setLocalGdprConsents] = useState<{
    marketing_emails: boolean;    // Local state for marketing email consent
  }>({
    marketing_emails: false       // Default to false for privacy-first approach
  });

  // Local state for cookie consent toggles
  // Separate from GDPR consents as they have different update mechanisms
  const [localCookieConsents, setLocalCookieConsents] = useState<{
    analytics_cookies: boolean;   // Local state for analytics cookie consent
    marketing_cookies: boolean;   // Local state for marketing cookie consent
  }>({
    analytics_cookies: false,     // Default to false for privacy-first approach
    marketing_cookies: false      // Default to false for privacy-first approach
  });

  // Load consent status when component mounts
  useEffect(() => {
    fetchConsentStatus();
  }, []);

  /**
   * Fetch current consent status from both GDPR and cookie consent APIs
   * 
   * This function retrieves the user's current consent preferences from the server
   * and updates both the display state and local editing state. It handles:
   * - Authentication errors (redirects to sign-in)
   * - Network errors (shows user-friendly messages)
   * - Data normalization (provides sensible defaults)
   */
  const fetchConsentStatus = async () => {
    try {
      setIsLoading(true);
      
      // Fetch both GDPR consents and cookie consents concurrently for better performance
      const [gdprResponse, cookieResponse] = await Promise.all([
        fetch('/api/gdpr/consent'),           // Get GDPR consent data
        fetch('/api/gdpr/cookie-consent')     // Get cookie preference data
      ]);

      // Handle authentication errors specifically
      if (!gdprResponse.ok || !cookieResponse.ok) {
        if (gdprResponse.status === 401 || cookieResponse.status === 401) {
          toast.error('Please sign in to manage your consent preferences');
          return;
        }
        throw new Error('Failed to fetch consent status');
      }

      // Parse response data concurrently
      const [gdprData, cookieData] = await Promise.all([
        gdprResponse.json(),
        cookieResponse.json()
      ]);

      // Normalize the data structure with sensible defaults
      const status: ConsentStatus = {
        gdprConsents: gdprData.consents || {},                    // GDPR consent data
        cookieConsents: cookieData.consent || {                   // Cookie consent data with defaults
          necessary_cookies: true,                                // Always required
          analytics_cookies: false,                               // Default to privacy-first
          marketing_cookies: false,                               // Default to privacy-first
          consent_date: null                                      // No previous consent
        },
        consentHistory: gdprData.consent_history || []            // Consent change history
      };

      setConsentStatus(status);

      // Update local state to match server state
      // This ensures the UI toggles reflect the current server state
      setLocalGdprConsents({
        marketing_emails: status.gdprConsents.marketing_emails?.given || false
      });

      setLocalCookieConsents({
        analytics_cookies: status.cookieConsents.analytics_cookies,
        marketing_cookies: status.cookieConsents.marketing_cookies
      });

    } catch (error: any) {
      // Show user-friendly error message
      toast.error(error.message || 'Failed to fetch consent status');
    } finally {
      // Always stop loading indicator
      setIsLoading(false);
    }
  };

  /**
   * Update GDPR consent preferences on the server
   * 
   * This function sends the user's updated GDPR consent preferences to the server.
   * It maintains required consents (privacy policy, terms of service) while
   * allowing users to control optional consents (marketing emails).
   * 
   * GDPR Compliance: Implements Article 7 requirements for consent management
   */
  const updateGdprConsents = async () => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/gdpr/consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          consents: {
            privacy_policy: true,                                 // Always maintain required consent
            terms_of_service: true,                               // Always maintain required consent
            marketing_emails: localGdprConsents.marketing_emails, // User's choice for optional consent
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update GDPR consents');
      }

      toast.success('GDPR consent preferences updated successfully');
      await fetchConsentStatus(); // Refresh data to show updated timestamps and status
    } catch (error: any) {
      toast.error(error.message || 'Failed to update GDPR consents');
    } finally {
      setIsUpdating(false);
    }
  };

  /**
   * Update cookie consent preferences on the server
   * 
   * This function sends the user's updated cookie preferences to the server.
   * It maintains necessary cookies (required for site functionality) while
   * allowing users to control optional cookies (analytics, marketing).
   * 
   * Cookie Categories:
   * - Necessary: Required for site functionality (always enabled)
   * - Analytics: Help improve user experience (optional)
   * - Marketing: Used for advertising and personalization (optional)
   */
  const updateCookieConsents = async () => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/gdpr/cookie-consent', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          necessary_cookies: true,                                  // Always required for site functionality
          analytics_cookies: localCookieConsents.analytics_cookies, // User's choice for analytics
          marketing_cookies: localCookieConsents.marketing_cookies,  // User's choice for marketing
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update cookie consents');
      }

      toast.success('Cookie preferences updated successfully');
      await fetchConsentStatus(); // Refresh data to show updated timestamps and status
    } catch (error: any) {
      toast.error(error.message || 'Failed to update cookie preferences');
    } finally {
      setIsUpdating(false);
    }
  };

  /**
   * Withdraw all optional consents in a single action
   * 
   * This function provides users with a quick way to withdraw all optional
   * consents while maintaining required ones. This implements GDPR Article 7(3)
   * which states that withdrawal of consent should be as easy as giving consent.
   * 
   * Actions performed:
   * - Withdraw marketing email consent
   * - Disable analytics cookies
   * - Disable marketing cookies
   * - Maintain required consents (privacy policy, terms of service)
   * - Maintain necessary cookies (site functionality)
   */
  const withdrawAllConsents = async () => {
    // Confirm user intent with clear explanation of consequences
    if (!confirm('Are you sure you want to withdraw all optional consents? This will disable marketing communications and optional cookies.')) {
      return;
    }

    setIsUpdating(true);
    try {
      // Withdraw GDPR consents (except required ones)
      // This updates the consent records with withdrawal timestamps
      await fetch('/api/gdpr/consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          consents: {
            privacy_policy: true,     // Keep required consent
            terms_of_service: true,   // Keep required consent
            marketing_emails: false,  // Withdraw optional consent
          }
        }),
      });

      // Reset cookies to necessary only
      // This immediately stops optional tracking and marketing cookies
      await fetch('/api/gdpr/cookie-consent', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          necessary_cookies: true,    // Keep required cookies
          analytics_cookies: false,  // Disable optional analytics
          marketing_cookies: false,  // Disable optional marketing
        }),
      });

      toast.success('All optional consents have been withdrawn');
      await fetchConsentStatus(); // Refresh data to reflect changes
    } catch (error: any) {
      toast.error(error.message || 'Failed to withdraw consents');
    } finally {
      setIsUpdating(false);
    }
  };

  /**
   * Generate appropriate status badge for consent state
   * 
   * This helper function creates visual indicators for different consent states:
   * - Required consents: Always show as "Required" (cannot be withdrawn)
   * - Optional consents: Show as "Granted" (green) or "Withdrawn" (gray)
   * 
   * @param given - Whether the consent has been granted
   * @param required - Whether this consent is required (cannot be withdrawn)
   * @returns JSX badge component with appropriate styling and icon
   */
  const getConsentStatusBadge = (given: boolean, required: boolean = false) => {
    // Required consents are always shown as "Required" regardless of state
    if (required) {
      return <Badge variant="secondary">Required</Badge>;
    }
    
    // Optional consents show current state with appropriate colors and icons
    return given ? (
      <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
        <CheckCircle className="w-3 h-3 mr-1" />
        Granted
      </Badge>
    ) : (
      <Badge variant="outline" className="text-gray-600">
        <XCircle className="w-3 h-3 mr-1" />
        Withdrawn
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Loading consent preferences...</span>
      </div>
    );
  }

  if (!consentStatus) {
    return (
      <div className="text-center p-8">
        <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-yellow-500" />
        <p>Failed to load consent preferences</p>
        <Button onClick={fetchConsentStatus} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Consent Management</h2>
          <p className="text-muted-foreground">
            Manage your data processing and cookie preferences
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowHistory(!showHistory)}
          >
            <History className="w-4 h-4 mr-2" />
            {showHistory ? 'Hide' : 'Show'} History
          </Button>
          <Button
            variant="destructive"
            onClick={withdrawAllConsents}
            disabled={isUpdating}
          >
            {isUpdating ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <XCircle className="w-4 h-4 mr-2" />
            )}
            Withdraw All Optional
          </Button>
        </div>
      </div>

      {/* GDPR Consents */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-blue-500" />
            GDPR Data Processing Consents
          </CardTitle>
          <CardDescription>
            Your consent for different types of data processing activities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Privacy Policy - Required */}
          <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
            <div className="flex-1">
              <h4 className="font-medium">Privacy Policy Agreement</h4>
              <p className="text-sm text-muted-foreground">
                Required for using our service and processing your account data
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(true, true)}
                {consentStatus.gdprConsents.privacy_policy && (
                  <span className="text-xs text-muted-foreground">
                    Agreed on {new Date(consentStatus.gdprConsents.privacy_policy.date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch checked={true} disabled />
          </div>

          {/* Terms of Service - Required */}
          <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
            <div className="flex-1">
              <h4 className="font-medium">Terms of Service Agreement</h4>
              <p className="text-sm text-muted-foreground">
                Required for using our service and defining our relationship
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(true, true)}
                {consentStatus.gdprConsents.terms_of_service && (
                  <span className="text-xs text-muted-foreground">
                    Agreed on {new Date(consentStatus.gdprConsents.terms_of_service.date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch checked={true} disabled />
          </div>

          {/* Marketing Emails - Optional */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Marketing Communications
              </h4>
              <p className="text-sm text-muted-foreground">
                Receive promotional emails, product updates, and special offers
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(localGdprConsents.marketing_emails)}
                {consentStatus.gdprConsents.marketing_emails && (
                  <span className="text-xs text-muted-foreground">
                    Last updated {new Date(consentStatus.gdprConsents.marketing_emails.date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch
              checked={localGdprConsents.marketing_emails}
              onCheckedChange={(checked) => 
                setLocalGdprConsents(prev => ({ ...prev, marketing_emails: checked }))
              }
              disabled={isUpdating}
            />
          </div>

          <Button
            onClick={updateGdprConsents}
            disabled={isUpdating}
            className="w-full"
          >
            {isUpdating ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Shield className="w-4 h-4 mr-2" />
            )}
            Update GDPR Preferences
          </Button>
        </CardContent>
      </Card>

      {/* Cookie Consents */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cookie className="w-5 h-5 text-orange-500" />
            Cookie Preferences
          </CardTitle>
          <CardDescription>
            Control which cookies we can use to enhance your experience
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Necessary Cookies - Required */}
          <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
            <div className="flex-1">
              <h4 className="font-medium">Necessary Cookies</h4>
              <p className="text-sm text-muted-foreground">
                Essential for website functionality, security, and your account access
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(true, true)}
                <span className="text-xs text-muted-foreground">
                  Always active for security and functionality
                </span>
              </div>
            </div>
            <Switch checked={true} disabled />
          </div>

          {/* Analytics Cookies - Optional */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Analytics Cookies
              </h4>
              <p className="text-sm text-muted-foreground">
                Help us understand how you use our website to improve your experience
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(localCookieConsents.analytics_cookies)}
                {consentStatus.cookieConsents.consent_date && (
                  <span className="text-xs text-muted-foreground">
                    Last updated {new Date(consentStatus.cookieConsents.consent_date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch
              checked={localCookieConsents.analytics_cookies}
              onCheckedChange={(checked) =>
                setLocalCookieConsents(prev => ({ ...prev, analytics_cookies: checked }))
              }
              disabled={isUpdating}
            />
          </div>

          {/* Marketing Cookies - Optional */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium flex items-center gap-2">
                <Target className="w-4 h-4" />
                Marketing Cookies
              </h4>
              <p className="text-sm text-muted-foreground">
                Used to show you relevant advertisements and measure campaign effectiveness
              </p>
              <div className="flex items-center gap-2 mt-2">
                {getConsentStatusBadge(localCookieConsents.marketing_cookies)}
                {consentStatus.cookieConsents.consent_date && (
                  <span className="text-xs text-muted-foreground">
                    Last updated {new Date(consentStatus.cookieConsents.consent_date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            <Switch
              checked={localCookieConsents.marketing_cookies}
              onCheckedChange={(checked) =>
                setLocalCookieConsents(prev => ({ ...prev, marketing_cookies: checked }))
              }
              disabled={isUpdating}
            />
          </div>

          <Button
            onClick={updateCookieConsents}
            disabled={isUpdating}
            className="w-full"
          >
            {isUpdating ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Cookie className="w-4 h-4 mr-2" />
            )}
            Update Cookie Preferences
          </Button>
        </CardContent>
      </Card>

      {/* Consent History */}
      {showHistory && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="w-5 h-5 text-purple-500" />
              Consent History
            </CardTitle>
            <CardDescription>
              Complete history of your consent decisions for audit and transparency
            </CardDescription>
          </CardHeader>
          <CardContent>
            {consentStatus.consentHistory.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Info className="w-8 h-8 mx-auto mb-2" />
                <p>No consent history available</p>
              </div>
            ) : (
              <div className="space-y-3">
                {consentStatus.consentHistory.slice(0, 10).map((consent: any, index: number) => (
                  <div key={consent.id || index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium capitalize">
                          {consent.consent_type?.replace('_', ' ')}
                        </span>
                        {consent.consent_given ? (
                          <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Granted
                          </Badge>
                        ) : (
                          <Badge variant="outline">
                            Withdrawn
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {new Date(consent.consent_date).toLocaleString()}
                        </span>
                        {consent.withdrawal_date && (
                          <span className="flex items-center gap-1">
                            <XCircle className="w-3 h-3" />
                            Withdrawn {new Date(consent.withdrawal_date).toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {consentStatus.consentHistory.length > 10 && (
                  <p className="text-center text-sm text-muted-foreground">
                    Showing 10 most recent entries of {consentStatus.consentHistory.length} total
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Information Card */}
      <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-500 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                Your Privacy Rights
              </p>
              <p className="text-blue-800 dark:text-blue-200">
                You can change these preferences at any time. Withdrawing consent will not affect
                the lawfulness of processing based on consent before its withdrawal. For more information,
                see our <a href="/privacy" className="underline">Privacy Policy</a>.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
