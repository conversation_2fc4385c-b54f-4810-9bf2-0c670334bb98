"use client";

import { useState, useEffect } from "react";
import { Sidebar } from "./sidebar";
import { Header } from "./header";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  SidebarProvider,
  SidebarInset,
  SidebarTrigger
} from "@/components/ui/sidebar";

interface ModernDashboardLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
}

export function ModernDashboardLayout({
  children,
  title,
  subtitle
}: ModernDashboardLayoutProps) {
  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex h-screen w-full">
        <Sidebar />
        <SidebarInset className="flex flex-1 flex-col overflow-hidden">
          {/* Header */}
          <Header
            title={title}
            subtitle={subtitle}
            showMenuButton={true}
          />

          {/* Page Content */}
          <main className="flex-1 overflow-y-auto bg-neutral-50 dark:bg-neutral-950">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="p-4 md:p-6"
            >
              {children}
            </motion.div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
