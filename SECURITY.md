# Security Configuration Guide

## Source Code Exposure Prevention

### The Issue
In development mode, Next.js generates source maps that allow developers to debug minified code. However, if these source maps are deployed to production, anyone can view your entire source code through browser DevTools.

### What We've Fixed
1. **Disabled Production Source Maps**: Set `productionBrowserSourceMaps: false`
2. **Webpack Configuration**: Disabled devtool in production builds
3. **Security Headers**: Added comprehensive security headers

### Production Deployment Checklist

#### Before Deploying:
- [ ] Ensure `NODE_ENV=production` is set
- [ ] Verify source maps are disabled in production build
- [ ] Test production build locally: `bun build && bun start`
- [ ] Check DevTools Sources tab - should not show source code
- [ ] Verify all environment variables are properly set
- [ ] Ensure sensitive keys are not exposed in client-side code

#### Environment Variables:
```bash
NODE_ENV=production
SUPABASE_URL=your_production_supabase_url
SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_key
BETTER_AUTH_SECRET=your_strong_production_secret
BETTER_AUTH_URL=https://yourdomain.com
NEXT_PUBLIC_APP_URL=https://yourdomain.com
```

#### Security Headers Implemented:
- `X-Content-Type-Options: nosniff` - Prevents MIME type sniffing
- `X-Frame-Options: DENY` - Prevents clickjacking
- `X-XSS-Protection: 1; mode=block` - Enables XSS filtering
- `Referrer-Policy: strict-origin-when-cross-origin` - Controls referrer information
- `Permissions-Policy` - Restricts access to browser features

### Testing Security
1. Build for production: `bun build`
2. Start production server: `bun start`
3. Open DevTools → Sources → should NOT see your source code
4. Check Network tab for any exposed source map files (.map)

### Account Deletion Security (GDPR Compliance)
**CRITICAL**: Account deletion must remove ALL user data from the database AND cancel all subscriptions in payment providers.

#### What We Fixed:
- **CRITICAL BILLING FIX**: Added Creem subscription cancellation before account deletion
- Added explicit deletion from `users` table after Better Auth deletion
- Enhanced error handling for failed user deletions and subscription cancellations
- Added verification scripts to check deletion completeness
- Created test endpoints for deletion verification (dev only)
- **Account deletion now ABORTS if subscriptions cannot be canceled** to prevent continued billing

#### Testing Account Deletion:
1. **Verify current state**: `curl "http://localhost:3000/api/test/account-deletion?action=verify"`
2. **Check user data**: `curl "http://localhost:3000/api/test/account-deletion?action=check-user-data&userId=USER_ID"`
3. **List all users**: `curl "http://localhost:3000/api/test/account-deletion?action=list-users"`

#### Testing Creem Integration:
1. **Test Creem connection**: `curl "http://localhost:3000/api/test/creem-integration?action=test-connection"`
2. **Check user subscriptions**: `curl "http://localhost:3000/api/test/creem-integration?action=list-user-subscriptions&userId=USER_ID"`
3. **Test subscription cancellation**: `curl "http://localhost:3000/api/test/creem-integration?action=test-cancel-subscription&subscriptionId=SUB_ID"`
4. **Verify account deletion eligibility**: `curl "http://localhost:3000/api/test/creem-integration?action=simulate-account-deletion-check&userId=USER_ID"`

#### Manual Verification:
```bash
# Run the verification script
bun run scripts/verify-account-deletion.ts verify

# Check for orphaned records
bun run scripts/verify-account-deletion.ts cleanup
```

### Additional Security Recommendations
1. Use HTTPS in production
2. Implement Content Security Policy (CSP)
3. Regular security audits: `bun audit`
4. Keep dependencies updated
5. Use environment-specific configurations
6. Implement proper error handling (don't expose stack traces)
7. Use secure session management
8. Implement rate limiting
9. Validate all user inputs
10. Use CSRF protection
11. **Test account deletion regularly** to ensure GDPR compliance
