/**
 * Data Retention Configuration and Management
 * 
 * GDPR Article 5(1)(e) - Storage limitation principle
 * Personal data shall be kept in a form which permits identification of data subjects 
 * for no longer than is necessary for the purposes for which the personal data are processed
 * 
 * This module provides:
 * - Centralized retention policy configuration
 * - Automated data cleanup functionality
 * - GDPR compliance monitoring and reporting
 * - Support for different deletion methods (hard delete, anonymization, archiving)
 */

import { supabaseAdmin } from '@/lib/supabase';

/**
 * Interface defining a data retention policy
 * Each policy specifies how long data should be kept and how it should be disposed of
 */
export interface RetentionPolicy {
  table: string;                    // Database table name this policy applies to
  retentionPeriodDays: number;      // How many days to keep data (-1 = never delete)
  dateColumn: string;               // Column to use for age calculation (e.g., 'created_at')
  description: string;              // Human-readable description of what this data is
  legalBasis: string;               // Legal justification for the retention period
  deletionMethod: 'hard_delete' | 'anonymize' | 'archive'; // How to dispose of expired data
  conditions?: string;              // Optional SQL WHERE conditions for selective retention
}

/**
 * Data Retention Policies Configuration
 * 
 * This array defines all retention policies for the application.
 * Each policy is based on legal requirements, business needs, and GDPR compliance.
 * Retention periods are carefully chosen to balance user privacy with operational needs.
 */
export const DATA_RETENTION_POLICIES: RetentionPolicy[] = [
  // Session data - short retention for security purposes
  // Sessions contain authentication tokens and should be cleaned up regularly
  {
    table: 'sessions',
    retentionPeriodDays: 90,                    // 3 months - balance between UX and security
    dateColumn: 'created_at',                   // Use session creation date
    description: 'User session data',
    legalBasis: 'Security and fraud prevention',
    deletionMethod: 'hard_delete'               // Complete removal - no need to keep session data
  },

  // Verification tokens - very short retention
  // These are temporary tokens for email verification, password reset, etc.
  {
    table: 'verifications',
    retentionPeriodDays: 7,                     // 1 week - tokens typically expire much sooner
    dateColumn: 'created_at',
    description: 'Email verification tokens',
    legalBasis: 'Account verification process',
    deletionMethod: 'hard_delete',
    conditions: "expires_at < NOW()"            // Only delete already-expired tokens for safety
  },

  // Audit logs - longer retention for compliance and security monitoring
  // These logs are crucial for security investigations and compliance audits
  {
    table: 'audit_logs',
    retentionPeriodDays: 1095,                  // 3 years - common compliance requirement
    dateColumn: 'created_at',
    description: 'Security and compliance audit logs',
    legalBasis: 'Legal compliance and security monitoring',
    deletionMethod: 'anonymize'                 // Keep logs for patterns but remove personal data
  },

  // Cookie consents - moderate retention for GDPR compliance
  // Need to prove consent was given, but not indefinitely
  {
    table: 'cookie_consents',
    retentionPeriodDays: 730,                   // 2 years - sufficient for consent proof
    dateColumn: 'created_at',
    description: 'Cookie consent records',
    legalBasis: 'GDPR consent documentation',
    deletionMethod: 'hard_delete',
    conditions: "user_id IS NOT NULL"           // Keep anonymous consents longer (less privacy risk)
  },

  // User consents - longer retention for legal proof of consent
  // These are explicit GDPR consents that may need to be proven in legal contexts
  {
    table: 'user_consents',
    retentionPeriodDays: 1095,                  // 3 years - legal proof requirement
    dateColumn: 'created_at',
    description: 'GDPR consent records',
    legalBasis: 'Legal proof of consent',
    deletionMethod: 'anonymize'                 // Keep consent records but remove personal identifiers
  },

  // Deletion requests - permanent retention for compliance
  // Must keep records of deletion requests to prove compliance with user requests
  {
    table: 'deletion_requests',
    retentionPeriodDays: -1,                    // Never delete - legal requirement
    dateColumn: 'created_at',
    description: 'Account deletion requests',
    legalBasis: 'Legal compliance and audit trail',
    deletionMethod: 'archive'                   // Move to long-term storage but don't delete
  },

  // Inactive user accounts - cleanup after extended inactivity
  // Removes abandoned accounts while giving users reasonable time to return
  {
    table: 'users',
    retentionPeriodDays: 1095,                  // 3 years - generous period for user return
    dateColumn: 'updated_at',                   // Use last activity date
    description: 'Inactive user accounts',
    legalBasis: 'Account maintenance and security',
    deletionMethod: 'hard_delete',
    conditions: "email_verified = false OR updated_at < NOW() - INTERVAL '1 year'" // Target unverified or very inactive accounts
  }
];

/**
 * Get retention policy for a specific table
 * 
 * @param tableName - Name of the database table
 * @returns The retention policy for the table, or undefined if no policy exists
 */
export function getRetentionPolicy(tableName: string): RetentionPolicy | undefined {
  return DATA_RETENTION_POLICIES.find(policy => policy.table === tableName);
}

/**
 * Calculate retention cutoff date for a policy
 * 
 * This determines the date before which all data should be considered expired
 * and eligible for cleanup according to the retention policy.
 * 
 * @param policy - The retention policy to calculate cutoff for
 * @returns Date object representing the cutoff (data older than this should be cleaned)
 */
export function getRetentionCutoffDate(policy: RetentionPolicy): Date {
  // Special case: -1 means never delete
  if (policy.retentionPeriodDays === -1) {
    // Return a very old date so nothing is considered expired
    return new Date('1970-01-01');
  }

  // Calculate cutoff date by subtracting retention period from current date
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - policy.retentionPeriodDays);
  return cutoffDate;
}

/**
 * Check if data retention cleanup is needed for a table
 * 
 * This function queries the database to determine how many records are eligible
 * for cleanup based on the retention policy. It's used for reporting and
 * planning cleanup operations.
 * 
 * @param policy - The retention policy to check
 * @returns Object containing whether cleanup is needed, record count, and cutoff date
 */
export async function checkRetentionNeeded(policy: RetentionPolicy): Promise<{
  needed: boolean;
  recordCount: number;
  cutoffDate: Date;
}> {
  // Skip check for policies that never delete data
  if (policy.retentionPeriodDays === -1) {
    return { needed: false, recordCount: 0, cutoffDate: new Date() };
  }

  // Calculate the cutoff date for this policy
  const cutoffDate = getRetentionCutoffDate(policy);

  // Build query to count records older than the cutoff date
  let query = supabaseAdmin
    .from(policy.table as any)
    .select('id', { count: 'exact', head: true })    // Count only, don't return data
    .lt(policy.dateColumn, cutoffDate.toISOString()); // Records older than cutoff

  // Apply additional conditions if specified in the policy
  // NOTE: This is a simplified implementation. In production, you'd want
  // a more robust way to handle custom SQL conditions safely.
  if (policy.conditions) {
    console.warn(`Custom conditions not applied for ${policy.table}: ${policy.conditions}`);
    // TODO: Implement safe condition parsing and application
  }

  // Execute the count query
  const { count, error } = await query;

  // Handle database errors gracefully
  if (error) {
    console.error(`Error checking retention for ${policy.table}:`, error);
    return { needed: false, recordCount: 0, cutoffDate };
  }

  // Return results indicating whether cleanup is needed
  return {
    needed: (count || 0) > 0,           // Cleanup needed if any records found
    recordCount: count || 0,            // Number of records eligible for cleanup
    cutoffDate                          // The cutoff date used for this check
  };
}

/**
 * Get data retention status for all policies
 * 
 * This function provides a comprehensive overview of the data retention
 * status across all configured policies. It's used by the admin dashboard
 * and monitoring systems to understand cleanup needs.
 * 
 * @returns Object containing detailed status for each policy and summary statistics
 */
export async function getDataRetentionStatus(): Promise<{
  policies: (RetentionPolicy & {
    recordCount: number;
    cutoffDate: Date;
    cleanupNeeded: boolean;
  })[];
  totalRecordsToCleanup: number;
}> {
  // Check retention status for all policies concurrently for better performance
  const results = await Promise.all(
    DATA_RETENTION_POLICIES.map(async (policy) => {
      // Get the retention status for this specific policy
      const status = await checkRetentionNeeded(policy);

      // Combine policy configuration with current status
      return {
        ...policy,                          // Original policy configuration
        recordCount: status.recordCount,    // How many records need cleanup
        cutoffDate: status.cutoffDate,      // The cutoff date used
        cleanupNeeded: status.needed        // Whether cleanup is needed
      };
    })
  );

  // Calculate total records across all policies that need cleanup
  // This gives a quick overview of the overall cleanup workload
  const totalRecordsToCleanup = results.reduce(
    (total, result) => total + (result.cleanupNeeded ? result.recordCount : 0),
    0
  );

  return {
    policies: results,              // Detailed status for each policy
    totalRecordsToCleanup          // Summary statistic for dashboard
  };
}

/**
 * Anonymize data by removing personal identifiers
 * 
 * This function implements GDPR-compliant anonymization by removing or replacing
 * personal identifiers while preserving the structure and non-personal aspects
 * of the data for legitimate business purposes (like security monitoring).
 * 
 * @param tableName - Name of the table containing data to anonymize
 * @param recordIds - Array of record IDs to anonymize
 */
export async function anonymizeData(tableName: string, recordIds: string[]): Promise<void> {
  // Define anonymization strategies for each table
  // Each strategy removes personal identifiers while preserving business value
  const anonymizationMap: Record<string, any> = {
    // Audit logs: Keep the action/event but remove personal identifiers
    audit_logs: {
      user_id: null,                                    // Remove user identification
      ip_address: null,                                 // Remove IP address (personal data)
      user_agent: 'anonymized',                         // Replace with generic value
      details: { anonymized: true, original_action: 'redacted' } // Mark as anonymized
    },

    // User consents: Keep consent type/date but remove personal identifiers
    user_consents: {
      user_id: null,                                    // Remove user identification
      ip_address: null,                                 // Remove IP address
      user_agent: 'anonymized'                          // Replace with generic value
      // Note: consent_type and consent_date are kept for compliance statistics
    }
  };

  // Get the anonymization strategy for this table
  const updateData = anonymizationMap[tableName];
  if (!updateData) {
    throw new Error(`No anonymization mapping defined for table: ${tableName}`);
  }

  // Execute the anonymization update
  const { error } = await supabaseAdmin
    .from(tableName as any)
    .update(updateData)                                 // Apply anonymization mapping
    .in('id', recordIds);                               // Only to specified records

  // Handle database errors
  if (error) {
    throw new Error(`Failed to anonymize data in ${tableName}: ${error.message}`);
  }
}

/**
 * Execute data retention cleanup for a specific policy
 * 
 * This is the main function that performs the actual data cleanup according
 * to a retention policy. It handles different deletion methods and provides
 * comprehensive audit logging for compliance purposes.
 * 
 * @param policy - The retention policy to execute
 * @returns Object indicating success/failure and number of records processed
 */
export async function executeRetentionCleanup(policy: RetentionPolicy): Promise<{
  success: boolean;
  recordsProcessed: number;
  error?: string;
}> {
  try {
    // Skip policies that never delete data
    if (policy.retentionPeriodDays === -1) {
      return { success: true, recordsProcessed: 0 };
    }

    // Calculate the cutoff date for this cleanup operation
    const cutoffDate = getRetentionCutoffDate(policy);

    // First, identify all records that need to be processed
    // We select IDs first to know exactly what we're operating on
    let selectQuery = supabaseAdmin
      .from(policy.table as any)
      .select('id')                                     // Only need IDs for processing
      .lt(policy.dateColumn, cutoffDate.toISOString()); // Records older than cutoff

    const { data: recordsToProcess, error: selectError } = await selectQuery;

    // Handle database query errors
    if (selectError) {
      throw new Error(`Failed to select records: ${selectError.message}`);
    }

    // If no records need processing, return success with zero count
    if (!recordsToProcess || recordsToProcess.length === 0) {
      return { success: true, recordsProcessed: 0 };
    }

    // Extract record IDs for batch operations
    // Ensure we have valid data before processing
    const recordIds = recordsToProcess.map(record => {
      if (typeof record === 'object' && record !== null && 'id' in record) {
        return (record as { id: string }).id;
      }
      throw new Error('Invalid record format: missing id field');
    });

    // Execute the appropriate deletion method based on policy configuration
    switch (policy.deletionMethod) {
      case 'hard_delete':
        // Permanently delete records from the database
        const { error: deleteError } = await supabaseAdmin
          .from(policy.table as any)
          .delete()
          .in('id', recordIds);                         // Delete only the identified records

        if (deleteError) {
          throw new Error(`Failed to delete records: ${deleteError.message}`);
        }
        break;

      case 'anonymize':
        // Remove personal identifiers but keep records for business purposes
        await anonymizeData(policy.table, recordIds);
        break;

      case 'archive':
        // Move data to long-term storage (implementation depends on infrastructure)
        // For now, we log that archiving is needed - in production, you'd implement
        // actual archiving to cold storage, separate database, or file system
        console.log(`Archiving needed for ${recordIds.length} records in ${policy.table}`);
        // TODO: Implement actual archiving mechanism
        break;
    }

    // Create comprehensive audit log entry for compliance and monitoring
    // This is crucial for demonstrating GDPR compliance and tracking system behavior
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: null,                                  // System-initiated action
        action: 'data_retention_cleanup',               // Specific action type
        resource_type: policy.table,                    // What table was affected
        details: {
          policy_description: policy.description,       // Human-readable policy description
          retention_period_days: policy.retentionPeriodDays, // Retention period used
          deletion_method: policy.deletionMethod,       // How data was processed
          records_processed: recordIds.length,          // Number of records affected
          cutoff_date: cutoffDate.toISOString(),        // Cutoff date used
          legal_basis: policy.legalBasis                // Legal justification
        }
      });

    // Return success with count of processed records
    return {
      success: true,
      recordsProcessed: recordIds.length
    };

  } catch (error: any) {
    // Handle any errors that occur during cleanup
    // Return failure status with error details for debugging
    return {
      success: false,
      recordsProcessed: 0,
      error: error.message
    };
  }
}
