# Data Retention Setup Guide

This guide explains how to set up automated data retention policies for GDPR compliance.

## Overview

The data retention system automatically cleans up old data according to predefined policies, ensuring compliance with GDPR Article 5(1)(e) - Storage limitation principle.

## Retention Policies

| Table | Retention Period | Method | Legal Basis |
|-------|------------------|--------|-------------|
| `sessions` | 90 days | Hard Delete | Security and fraud prevention |
| `verifications` | 7 days | Hard Delete | Account verification process |
| `audit_logs` | 3 years | Anonymize | Legal compliance and security monitoring |
| `cookie_consents` | 2 years | Hard Delete | GDPR consent documentation |
| `user_consents` | 3 years | Anonymize | Legal proof of consent |
| `deletion_requests` | Never | Archive | Legal compliance and audit trail |
| `users` (inactive) | 3 years | Hard Delete | Account maintenance and security |

## Manual Cleanup

### Using the Admin Dashboard

1. Navigate to the admin panel
2. Access the Data Retention Dashboard
3. Review policies and records needing cleanup
4. Select tables to clean up
5. Run a dry run first to preview changes
6. Execute actual cleanup when ready

### Using the API

```bash
# Get retention status
curl -X GET /api/admin/data-retention \
  -H "Authorization: Bearer YOUR_TOKEN"

# Run dry run cleanup
curl -X POST /api/admin/data-retention \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "tables": ["sessions", "verifications"],
    "dryRun": true
  }'

# Execute actual cleanup
curl -X POST /api/admin/data-retention \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "tables": ["sessions", "verifications"],
    "dryRun": false
  }'
```

### Using the Command Line Script

```bash
# Preview what would be cleaned up
bun run scripts/data-retention-cleanup.ts --dry-run

# Clean up specific table
bun run scripts/data-retention-cleanup.ts --table=sessions

# Force cleanup of large datasets
bun run scripts/data-retention-cleanup.ts --force --verbose

# Show help
bun run scripts/data-retention-cleanup.ts --help
```

## Automated Cleanup (Recommended)

### Setting up Cron Jobs

Add these entries to your crontab for automated cleanup:

```bash
# Edit crontab
crontab -e

# Add these lines:

# Daily cleanup of short-retention data (sessions, verifications)
0 2 * * * cd /path/to/your/app && bun run scripts/data-retention-cleanup.ts --table=sessions --table=verifications

# Weekly cleanup of medium-retention data
0 3 * * 0 cd /path/to/your/app && bun run scripts/data-retention-cleanup.ts --table=cookie_consents

# Monthly cleanup of long-retention data (with dry run first)
0 4 1 * * cd /path/to/your/app && bun run scripts/data-retention-cleanup.ts --table=audit_logs --table=user_consents --dry-run

# Quarterly full cleanup (with force flag for large datasets)
0 5 1 */3 * cd /path/to/your/app && bun run scripts/data-retention-cleanup.ts --force
```

### Using GitHub Actions (for cloud deployments)

Create `.github/workflows/data-retention.yml`:

```yaml
name: Data Retention Cleanup

on:
  schedule:
    # Daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch: # Allow manual trigger

jobs:
  cleanup:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      
    - name: Install dependencies
      run: bun install
      
    - name: Run data retention cleanup
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: |
        # Daily cleanup of short-retention data
        bun run scripts/data-retention-cleanup.ts --table=sessions --table=verifications
        
        # Weekly cleanup on Sundays
        if [ $(date +%u) -eq 7 ]; then
          bun run scripts/data-retention-cleanup.ts --table=cookie_consents
        fi
        
        # Monthly cleanup on the 1st
        if [ $(date +%d) -eq 01 ]; then
          bun run scripts/data-retention-cleanup.ts --table=audit_logs --table=user_consents
        fi
```

### Using Vercel Cron Jobs

Create `vercel.json`:

```json
{
  "crons": [
    {
      "path": "/api/admin/data-retention/cron",
      "schedule": "0 2 * * *"
    }
  ]
}
```

Then create the cron endpoint at `app/api/admin/data-retention/cron/route.ts`.

## Monitoring and Alerts

### Audit Logging

All retention activities are logged to the `audit_logs` table with:
- Action type (`data_retention_cleanup`, `scheduled_retention_cleanup`)
- Tables processed
- Records affected
- Success/failure status

### Setting up Alerts

Monitor retention cleanup by querying audit logs:

```sql
-- Check recent retention activities
SELECT 
  created_at,
  action,
  details->>'total_records_processed' as records_processed,
  details->>'policies_processed' as policies_processed
FROM audit_logs 
WHERE action LIKE '%retention%' 
ORDER BY created_at DESC 
LIMIT 10;

-- Check for failed cleanups
SELECT *
FROM audit_logs 
WHERE action = 'scheduled_retention_error'
AND created_at > NOW() - INTERVAL '7 days';
```

## Security Considerations

1. **Admin Access**: Retention management requires admin privileges
2. **Dry Run First**: Always test with dry runs before actual cleanup
3. **Backup Strategy**: Ensure backups are in place before large cleanups
4. **Audit Trail**: All actions are logged for compliance
5. **Force Flag**: Use `--force` carefully for large datasets

## Compliance Notes

- **GDPR Article 5(1)(e)**: Data minimization and storage limitation
- **Right to Erasure**: Automated cleanup supports Article 17 compliance
- **Audit Requirements**: All retention activities are logged
- **Legal Basis**: Each policy documents its legal justification

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure service role key has proper permissions
2. **Large Datasets**: Use `--force` flag or process in smaller batches
3. **Failed Cleanups**: Check audit logs for error details
4. **Orphaned Records**: Run the account deletion verification script

### Recovery

If cleanup goes wrong:
1. Check audit logs for what was processed
2. Restore from backup if necessary
3. Review and adjust retention policies
4. Test with dry runs before re-running

## Customization

To modify retention policies, edit `lib/data-retention.ts`:

```typescript
export const DATA_RETENTION_POLICIES: RetentionPolicy[] = [
  {
    table: 'your_table',
    retentionPeriodDays: 365, // 1 year
    dateColumn: 'created_at',
    description: 'Your custom data',
    legalBasis: 'Your legal justification',
    deletionMethod: 'hard_delete'
  }
];
```

Remember to update this documentation when policies change!
