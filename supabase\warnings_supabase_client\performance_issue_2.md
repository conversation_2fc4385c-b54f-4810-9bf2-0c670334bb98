| name              | title                        | level | facing   | categories      | description                                                                                                                           | detail                                                                                                                                                                                                                                                                                                                                                                                                                                                          | remediation                                                                           | metadata                                                       | cache_key                                                                          |
| ----------------- | ---------------------------- | ----- | -------- | --------------- | ------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------- | -------------------------------------------------------------- | ---------------------------------------------------------------------------------- |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.user_consents\` has a row level security policy \`users_can_insert_own_consents\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.              | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"user_consents","type":"table","schema":"public"}      | auth_rls_init_plan_public_user_consents_users_can_insert_own_consents              |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.audit_logs\` has a row level security policy \`users_can_view_own_audit_logs\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                 | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"audit_logs","type":"table","schema":"public"}         | auth_rls_init_plan_public_audit_logs_users_can_view_own_audit_logs                 |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.deletion_requests\` has a row level security policy \`users_can_view_own_deletion_requests\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.   | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"deletion_requests","type":"table","schema":"public"}  | auth_rls_init_plan_public_deletion_requests_users_can_view_own_deletion_requests   |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.deletion_requests\` has a row level security policy \`users_can_create_own_deletion_requests\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info. | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"deletion_requests","type":"table","schema":"public"}  | auth_rls_init_plan_public_deletion_requests_users_can_create_own_deletion_requests |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.users\` has a row level security policy \`users_can_view_own_profile\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                         | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"users","type":"table","schema":"public"}              | auth_rls_init_plan_public_users_users_can_view_own_profile                         |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.users\` has a row level security policy \`users_can_update_own_profile\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                       | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"users","type":"table","schema":"public"}              | auth_rls_init_plan_public_users_users_can_update_own_profile                       |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.sessions\` has a row level security policy \`users_can_view_own_sessions\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                     | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"sessions","type":"table","schema":"public"}           | auth_rls_init_plan_public_sessions_users_can_view_own_sessions                     |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.sessions\` has a row level security policy \`users_can_delete_own_sessions\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                   | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"sessions","type":"table","schema":"public"}           | auth_rls_init_plan_public_sessions_users_can_delete_own_sessions                   |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.accounts\` has a row level security policy \`users_can_view_own_accounts\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                     | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"accounts","type":"table","schema":"public"}           | auth_rls_init_plan_public_accounts_users_can_view_own_accounts                     |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.subscriptions\` has a row level security policy \`users_can_view_own_subscriptions\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.           | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"subscriptions","type":"table","schema":"public"}      | auth_rls_init_plan_public_subscriptions_users_can_view_own_subscriptions           |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.one_time_purchases\` has a row level security policy \`users_can_view_own_purchases\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.          | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"one_time_purchases","type":"table","schema":"public"} | auth_rls_init_plan_public_one_time_purchases_users_can_view_own_purchases          |
| auth_rls_initplan | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row | Table \`public.user_consents\` has a row level security policy \`users_can_view_own_consents\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan | {"name":"user_consents","type":"table","schema":"public"}      | auth_rls_init_plan_public_user_consents_users_can_view_own_consents                |