/** @type {import('next').NextConfig} */
const nextConfig = {
  images: { domains: ["i.pravatar.cc"] },
  pageExtensions: ["ts", "tsx", "mdx"],

  // Security: Disable source maps in production to prevent source code exposure
  productionBrowserSourceMaps: false,

  // Disable server source maps as well for complete security
  serverRuntimeConfig: {},
  publicRuntimeConfig: {},

  // Webpack configuration for additional security
  webpack: (config, { dev, isServer }) => {
    // Disable source maps in production
    if (!dev) {
      config.devtool = false;
    }

    return config;
  },

  // Additional security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
