-- Processing Activities Register (GDPR Article 30)
-- This table stores comprehensive records of all data processing activities
-- as required by GDPR Article 30 for controllers and processors

-- Create processing activities table
CREATE TABLE IF NOT EXISTS public.processing_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Basic activity information
    activity_name TEXT NOT NULL,
    controller_name TEXT NOT NULL,
    controller_contact TEXT NOT NULL,
    dpo_contact TEXT,
    
    -- Processing details (stored as JSONB for flexibility)
    purposes JSONB NOT NULL DEFAULT '[]'::jsonb,
    legal_bases JSONB NOT NULL DEFAULT '[]'::jsonb,
    data_subject_categories JSONB NOT NULL DEFAULT '[]'::jsonb,
    personal_data_categories JSONB NOT NULL DEFAULT '[]'::jsonb,
    recipients JSONB DEFAULT '[]'::jsonb,
    third_country_transfers JSONB DEFAULT '[]'::jsonb,
    retention_periods JSONB NOT NULL DEFAULT '[]'::jsonb,
    security_measures JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Metadata
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    system_generated BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_processing_activities_activity_name ON public.processing_activities(activity_name);
CREATE INDEX IF NOT EXISTS idx_processing_activities_controller ON public.processing_activities(controller_name);
CREATE INDEX IF NOT EXISTS idx_processing_activities_user_id ON public.processing_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_processing_activities_system_generated ON public.processing_activities(system_generated);
CREATE INDEX IF NOT EXISTS idx_processing_activities_created_at ON public.processing_activities(created_at);

-- Create GIN indexes for JSONB columns to enable efficient searching
CREATE INDEX IF NOT EXISTS idx_processing_activities_purposes ON public.processing_activities USING GIN(purposes);
CREATE INDEX IF NOT EXISTS idx_processing_activities_legal_bases ON public.processing_activities USING GIN(legal_bases);
CREATE INDEX IF NOT EXISTS idx_processing_activities_data_categories ON public.processing_activities USING GIN(personal_data_categories);

-- Enable Row Level Security
ALTER TABLE public.processing_activities ENABLE ROW LEVEL SECURITY;

-- RLS Policies for processing activities
-- Admin users can view all processing activities
CREATE POLICY "Admin users can view all processing activities" ON public.processing_activities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = (select auth.uid()) 
            AND (email LIKE '%@admin.%' OR email = '<EMAIL>')
        ) OR
        (select auth.role()) = 'service_role'
    );

-- Service role can manage all processing activities
CREATE POLICY "Service role can manage all processing activities" ON public.processing_activities
    FOR ALL USING ((select auth.role()) = 'service_role');

-- Users can view processing activities they created (for transparency)
CREATE POLICY "Users can view own processing activities" ON public.processing_activities
    FOR SELECT USING ((select auth.uid()) = user_id);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_processing_activities_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_processing_activities_updated_at ON public.processing_activities;
CREATE TRIGGER update_processing_activities_updated_at
    BEFORE UPDATE ON public.processing_activities
    FOR EACH ROW EXECUTE FUNCTION update_processing_activities_updated_at();

-- Create processing activity log table for tracking changes
CREATE TABLE IF NOT EXISTS public.processing_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    processing_activity_id UUID REFERENCES public.processing_activities(id) ON DELETE CASCADE,
    
    -- What changed
    action TEXT NOT NULL, -- 'created', 'updated', 'deleted', 'accessed'
    changed_fields JSONB,
    old_values JSONB,
    new_values JSONB,
    
    -- Who made the change
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    ip_address INET,
    user_agent TEXT,
    
    -- When
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for processing activity logs
CREATE INDEX IF NOT EXISTS idx_processing_activity_logs_activity_id ON public.processing_activity_logs(processing_activity_id);
CREATE INDEX IF NOT EXISTS idx_processing_activity_logs_action ON public.processing_activity_logs(action);
CREATE INDEX IF NOT EXISTS idx_processing_activity_logs_user_id ON public.processing_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_processing_activity_logs_created_at ON public.processing_activity_logs(created_at);

-- Enable RLS on processing activity logs
ALTER TABLE public.processing_activity_logs ENABLE ROW LEVEL SECURITY;

-- RLS policies for processing activity logs
CREATE POLICY "Admin users can view all processing activity logs" ON public.processing_activity_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = (select auth.uid()) 
            AND (email LIKE '%@admin.%' OR email = '<EMAIL>')
        ) OR
        (select auth.role()) = 'service_role'
    );

CREATE POLICY "Service role can manage all processing activity logs" ON public.processing_activity_logs
    FOR ALL USING ((select auth.role()) = 'service_role');

-- Create function to automatically log processing activity changes
CREATE OR REPLACE FUNCTION log_processing_activity_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Log the change
    INSERT INTO public.processing_activity_logs (
        processing_activity_id,
        action,
        changed_fields,
        old_values,
        new_values,
        user_id
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        CASE 
            WHEN TG_OP = 'INSERT' THEN 'created'
            WHEN TG_OP = 'UPDATE' THEN 'updated'
            WHEN TG_OP = 'DELETE' THEN 'deleted'
        END,
        CASE 
            WHEN TG_OP = 'UPDATE' THEN 
                (SELECT jsonb_object_agg(key, value) 
                 FROM jsonb_each(to_jsonb(NEW)) 
                 WHERE to_jsonb(NEW) -> key != to_jsonb(OLD) -> key)
            ELSE NULL
        END,
        CASE WHEN TG_OP != 'INSERT' THEN to_jsonb(OLD) ELSE NULL END,
        CASE WHEN TG_OP != 'DELETE' THEN to_jsonb(NEW) ELSE NULL END,
        COALESCE(NEW.user_id, OLD.user_id)
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Create triggers for automatic logging
DROP TRIGGER IF EXISTS processing_activity_changes_trigger ON public.processing_activities;
CREATE TRIGGER processing_activity_changes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.processing_activities
    FOR EACH ROW EXECUTE FUNCTION log_processing_activity_changes();

-- Create view for easy reporting
CREATE OR REPLACE VIEW public.processing_activities_report AS
SELECT 
    pa.id,
    pa.activity_name,
    pa.controller_name,
    pa.controller_contact,
    pa.dpo_contact,
    jsonb_array_length(pa.purposes) as purpose_count,
    jsonb_array_length(pa.legal_bases) as legal_basis_count,
    jsonb_array_length(pa.data_subject_categories) as data_subject_category_count,
    jsonb_array_length(pa.personal_data_categories) as personal_data_category_count,
    CASE WHEN pa.recipients IS NOT NULL THEN jsonb_array_length(pa.recipients) ELSE 0 END as recipient_count,
    CASE WHEN pa.third_country_transfers IS NOT NULL THEN jsonb_array_length(pa.third_country_transfers) ELSE 0 END as transfer_count,
    jsonb_array_length(pa.retention_periods) as retention_period_count,
    jsonb_array_length(pa.security_measures) as security_measure_count,
    pa.system_generated,
    pa.created_at,
    pa.updated_at,
    u.email as created_by_email
FROM public.processing_activities pa
LEFT JOIN public.users u ON pa.user_id = u.id
ORDER BY pa.created_at DESC;

-- Grant appropriate permissions
GRANT SELECT ON public.processing_activities_report TO authenticated;
GRANT SELECT ON public.processing_activities TO authenticated;
GRANT SELECT ON public.processing_activity_logs TO authenticated;

-- Comments for documentation
COMMENT ON TABLE public.processing_activities IS 'GDPR Article 30 Processing Activity Register - Records of all data processing activities';
COMMENT ON TABLE public.processing_activity_logs IS 'Audit trail for changes to processing activities';
COMMENT ON VIEW public.processing_activities_report IS 'Reporting view for processing activities with summary statistics';

COMMENT ON COLUMN public.processing_activities.activity_name IS 'Name and description of the processing activity';
COMMENT ON COLUMN public.processing_activities.controller_name IS 'Name of the data controller';
COMMENT ON COLUMN public.processing_activities.controller_contact IS 'Contact details of the controller';
COMMENT ON COLUMN public.processing_activities.dpo_contact IS 'Data Protection Officer contact details (if applicable)';
COMMENT ON COLUMN public.processing_activities.purposes IS 'Purposes of the processing (JSONB array)';
COMMENT ON COLUMN public.processing_activities.legal_bases IS 'Legal bases for processing under GDPR Article 6 (JSONB array)';
COMMENT ON COLUMN public.processing_activities.data_subject_categories IS 'Categories of data subjects (JSONB array)';
COMMENT ON COLUMN public.processing_activities.personal_data_categories IS 'Categories of personal data processed (JSONB array)';
COMMENT ON COLUMN public.processing_activities.recipients IS 'Recipients or categories of recipients (JSONB array)';
COMMENT ON COLUMN public.processing_activities.third_country_transfers IS 'Information about international transfers (JSONB array)';
COMMENT ON COLUMN public.processing_activities.retention_periods IS 'Data retention information (JSONB array)';
COMMENT ON COLUMN public.processing_activities.security_measures IS 'Technical and organizational security measures (JSONB array)';
COMMENT ON COLUMN public.processing_activities.system_generated IS 'Whether this activity was automatically generated by the system';
