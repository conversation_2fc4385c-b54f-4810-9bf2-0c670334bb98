-- ============================================================================
-- AUTH RLS PERFORMANCE OPTIMIZATION - FIX AUTH INITIALIZATION PLAN WARNINGS
-- ============================================================================
-- This migration fixes Supabase "Auth RLS Initialization Plan" warnings by
-- replacing direct auth.uid() calls with (select auth.uid()) in RLS policies.
--
-- Performance Impact:
-- - Before: auth.uid() evaluated for each row (O(n) complexity)
-- - After: (select auth.uid()) evaluated once per query (O(1) complexity)
-- - Significant performance improvement for large result sets
-- ============================================================================

-- ============================================================================
-- USERS TABLE OPTIMIZATION
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "users_can_view_own_profile" ON public.users;
DROP POLICY IF EXISTS "users_can_update_own_profile" ON public.users;

-- Recreate with optimized auth function calls
CREATE POLICY "users_can_view_own_profile" ON public.users
    FOR SELECT TO authenticated
    USING ((select auth.uid()) = id);

CREATE POLICY "users_can_update_own_profile" ON public.users
    FOR UPDATE TO authenticated
    USING ((select auth.uid()) = id)
    WITH CHECK ((select auth.uid()) = id);

-- ============================================================================
-- SESSIONS TABLE OPTIMIZATION
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "users_can_view_own_sessions" ON public.sessions;
DROP POLICY IF EXISTS "users_can_delete_own_sessions" ON public.sessions;

-- Recreate with optimized auth function calls
CREATE POLICY "users_can_view_own_sessions" ON public.sessions
    FOR SELECT TO authenticated
    USING ((select auth.uid()) = user_id);

CREATE POLICY "users_can_delete_own_sessions" ON public.sessions
    FOR DELETE TO authenticated
    USING ((select auth.uid()) = user_id);

-- ============================================================================
-- ACCOUNTS TABLE OPTIMIZATION
-- ============================================================================

-- Drop existing policy
DROP POLICY IF EXISTS "users_can_view_own_accounts" ON public.accounts;

-- Recreate with optimized auth function calls
CREATE POLICY "users_can_view_own_accounts" ON public.accounts
    FOR SELECT TO authenticated
    USING ((select auth.uid()) = user_id);

-- ============================================================================
-- SUBSCRIPTIONS TABLE OPTIMIZATION
-- ============================================================================

-- Drop existing policy
DROP POLICY IF EXISTS "users_can_view_own_subscriptions" ON public.subscriptions;

-- Recreate with optimized auth function calls
CREATE POLICY "users_can_view_own_subscriptions" ON public.subscriptions
    FOR SELECT TO authenticated
    USING ((select auth.uid()) = user_id);

-- ============================================================================
-- ONE-TIME PURCHASES TABLE OPTIMIZATION
-- ============================================================================

-- Drop existing policy
DROP POLICY IF EXISTS "users_can_view_own_purchases" ON public.one_time_purchases;

-- Recreate with optimized auth function calls
CREATE POLICY "users_can_view_own_purchases" ON public.one_time_purchases
    FOR SELECT TO authenticated
    USING ((select auth.uid()) = user_id);

-- ============================================================================
-- USER CONSENTS TABLE OPTIMIZATION
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "users_can_view_own_consents" ON public.user_consents;
DROP POLICY IF EXISTS "users_can_insert_own_consents" ON public.user_consents;

-- Recreate with optimized auth function calls
CREATE POLICY "users_can_view_own_consents" ON public.user_consents
    FOR SELECT TO authenticated
    USING ((select auth.uid()) = user_id);

CREATE POLICY "users_can_insert_own_consents" ON public.user_consents
    FOR INSERT TO authenticated
    WITH CHECK ((select auth.uid()) = user_id);

-- ============================================================================
-- AUDIT LOGS TABLE OPTIMIZATION
-- ============================================================================

-- Drop existing policy
DROP POLICY IF EXISTS "users_can_view_own_audit_logs" ON public.audit_logs;

-- Recreate with optimized auth function calls
CREATE POLICY "users_can_view_own_audit_logs" ON public.audit_logs
    FOR SELECT TO authenticated
    USING ((select auth.uid()) = user_id);

-- ============================================================================
-- DELETION REQUESTS TABLE OPTIMIZATION
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "users_can_view_own_deletion_requests" ON public.deletion_requests;
DROP POLICY IF EXISTS "users_can_create_own_deletion_requests" ON public.deletion_requests;

-- Recreate with optimized auth function calls
CREATE POLICY "users_can_view_own_deletion_requests" ON public.deletion_requests
    FOR SELECT TO authenticated
    USING ((select auth.uid()) = user_id);

CREATE POLICY "users_can_create_own_deletion_requests" ON public.deletion_requests
    FOR INSERT TO authenticated
    WITH CHECK ((select auth.uid()) = user_id);

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================
-- Run these queries after migration to verify optimization:
-- 
-- Check that no policies use direct auth.uid() calls:
-- SELECT tablename, policyname, qual, with_check 
-- FROM pg_policies 
-- WHERE schemaname = 'public' 
-- AND (qual LIKE '%auth.uid()%' OR with_check LIKE '%auth.uid()%')
-- AND policyname NOT LIKE '%service_role%';
-- 
-- Should return no rows if successful (excluding service_role policies which don't use auth.uid())
-- 
-- Check that optimized policies use (select auth.uid()):
-- SELECT tablename, policyname, qual, with_check 
-- FROM pg_policies 
-- WHERE schemaname = 'public' 
-- AND (qual LIKE '%(select auth.uid())%' OR with_check LIKE '%(select auth.uid())%');
-- 
-- Should return all user-facing policies with optimized auth calls
