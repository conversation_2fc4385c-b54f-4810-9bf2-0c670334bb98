import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";
import {
  getProcessingActivities,
  logProcessingActivity,
  initializeStandardActivities,
  ProcessingActivity
} from "@/lib/processing-activity-register";

/**
 * GET /api/admin/processing-activities
 * 
 * Retrieve all processing activities from the register
 * GDPR Article 30 compliance endpoint for processing activity records
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    // For demo purposes, we'll allow any authenticated user
    // In production, you'd check for admin role
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" }, 
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format'); // 'summary' or 'detailed'
    const category = searchParams.get('category'); // Filter by data category
    const legalBasis = searchParams.get('legal_basis'); // Filter by legal basis

    // Fetch processing activities
    const activities = await getProcessingActivities();

    // Apply filters if specified
    let filteredActivities = activities;
    
    if (category) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.personal_data_categories.includes(category)
      );
    }

    if (legalBasis) {
      filteredActivities = filteredActivities.filter(activity => 
        activity.legal_bases.includes(legalBasis)
      );
    }

    // Get summary statistics
    const summary = {
      totalActivities: filteredActivities.length,
      systemGenerated: filteredActivities.filter(a => a.system_generated).length,
      userGenerated: filteredActivities.filter(a => !a.system_generated).length,
      legalBasisBreakdown: getLegalBasisBreakdown(filteredActivities),
      dataSubjectCategories: getDataSubjectCategoryBreakdown(filteredActivities),
      personalDataCategories: getPersonalDataCategoryBreakdown(filteredActivities),
      lastUpdated: filteredActivities.length > 0 
        ? Math.max(...filteredActivities.map(a => new Date(a.updated_at || a.created_at || '').getTime()))
        : null
    };

    // Return format based on request
    if (format === 'summary') {
      return NextResponse.json({
        success: true,
        summary,
        activities: filteredActivities.map(activity => ({
          id: activity.id,
          activity_name: activity.activity_name,
          controller_name: activity.controller_name,
          purposes: activity.purposes,
          legal_bases: activity.legal_bases,
          data_subject_categories: activity.data_subject_categories,
          personal_data_categories: activity.personal_data_categories,
          system_generated: activity.system_generated,
          created_at: activity.created_at,
          updated_at: activity.updated_at
        }))
      });
    }

    return NextResponse.json({
      success: true,
      summary,
      activities: filteredActivities
    });

  } catch (error: any) {
    console.error('Processing activities fetch error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/processing-activities
 * 
 * Create a new processing activity record
 * GDPR Article 30 compliance endpoint for documenting processing activities
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      activity_name,
      controller_name,
      controller_contact,
      dpo_contact,
      purposes,
      legal_bases,
      data_subject_categories,
      personal_data_categories,
      recipients,
      third_country_transfers,
      retention_periods,
      security_measures,
      initialize_standard
    } = body;

    // Handle initialization of standard activities
    if (initialize_standard) {
      await initializeStandardActivities();
      
      // Log the initialization
      await supabaseAdmin
        .from('audit_logs')
        .insert({
          user_id: session.user.id,
          action: 'processing_activities_initialized',
          resource_type: 'processing_activity_register',
          details: {
            action: 'Standard processing activities initialized',
            initiated_by: session.user.id
          },
          ip_address: request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: request.headers.get('user-agent') || 'unknown'
        });

      return NextResponse.json({
        success: true,
        message: "Standard processing activities initialized successfully"
      });
    }

    // Validate required fields
    if (!activity_name || !controller_name || !controller_contact || !purposes || !legal_bases) {
      return NextResponse.json(
        { error: "Missing required fields: activity_name, controller_name, controller_contact, purposes, legal_bases" },
        { status: 400 }
      );
    }

    // Create new processing activity
    const newActivity: Omit<ProcessingActivity, 'id' | 'created_at' | 'updated_at'> = {
      activity_name,
      controller_name,
      controller_contact,
      dpo_contact,
      purposes: Array.isArray(purposes) ? purposes : [purposes],
      legal_bases: Array.isArray(legal_bases) ? legal_bases : [legal_bases],
      data_subject_categories: Array.isArray(data_subject_categories) ? data_subject_categories : [data_subject_categories],
      personal_data_categories: Array.isArray(personal_data_categories) ? personal_data_categories : [personal_data_categories],
      recipients: recipients ? (Array.isArray(recipients) ? recipients : [recipients]) : [],
      third_country_transfers: third_country_transfers || [],
      retention_periods: Array.isArray(retention_periods) ? retention_periods : [],
      security_measures: Array.isArray(security_measures) ? security_measures : [],
      system_generated: false,
      user_id: session.user.id
    };

    const activityId = await logProcessingActivity(newActivity, session.user.id);

    if (!activityId) {
      throw new Error('Failed to create processing activity');
    }

    // Log the creation in audit logs
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: session.user.id,
        action: 'processing_activity_created',
        resource_type: 'processing_activity',
        resource_id: activityId,
        details: {
          activity_name,
          controller_name,
          purposes: newActivity.purposes,
          legal_bases: newActivity.legal_bases
        },
        ip_address: request.headers.get('x-forwarded-for') || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown'
      });

    return NextResponse.json({
      success: true,
      message: "Processing activity created successfully",
      activity_id: activityId
    });

  } catch (error: any) {
    console.error('Processing activity creation error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/processing-activities
 * 
 * Update an existing processing activity record
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Processing activity ID is required" },
        { status: 400 }
      );
    }

    // Update the processing activity
    const { error } = await supabaseAdmin
      .from('processing_activities')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      throw error;
    }

    // Log the update
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: session.user.id,
        action: 'processing_activity_updated',
        resource_type: 'processing_activity',
        resource_id: id,
        details: {
          updated_fields: Object.keys(updateData),
          activity_name: updateData.activity_name
        },
        ip_address: request.headers.get('x-forwarded-for') || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown'
      });

    return NextResponse.json({
      success: true,
      message: "Processing activity updated successfully"
    });

  } catch (error: any) {
    console.error('Processing activity update error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper functions for statistics
function getLegalBasisBreakdown(activities: ProcessingActivity[]) {
  const breakdown: Record<string, number> = {};
  activities.forEach(activity => {
    activity.legal_bases.forEach(basis => {
      breakdown[basis] = (breakdown[basis] || 0) + 1;
    });
  });
  return breakdown;
}

function getDataSubjectCategoryBreakdown(activities: ProcessingActivity[]) {
  const breakdown: Record<string, number> = {};
  activities.forEach(activity => {
    activity.data_subject_categories.forEach(category => {
      breakdown[category] = (breakdown[category] || 0) + 1;
    });
  });
  return breakdown;
}

function getPersonalDataCategoryBreakdown(activities: ProcessingActivity[]) {
  const breakdown: Record<string, number> = {};
  activities.forEach(activity => {
    activity.personal_data_categories.forEach(category => {
      breakdown[category] = (breakdown[category] || 0) + 1;
    });
  });
  return breakdown;
}
