import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * POST /api/gdpr/objection
 * 
 * GDPR Article 21 - Right to Object
 * Handle user objections to processing of their personal data
 * 
 * This endpoint allows users to object to processing of their personal data
 * under GDPR Article 21. Different types of objections have different requirements:
 * - Direct marketing: Must be stopped immediately (Article 21(3))
 * - Legitimate interests: Organization can continue if compelling legitimate grounds exist
 * - Public task: Must consider user's particular situation
 * - Scientific research: Must assess if research is necessary for public interest
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate the user using session from request headers
    // Only authenticated users can make objection requests
    const session = await auth.api.getSession({ headers: headers() });
    
    // Return 401 Unauthorized if no valid session exists
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    // Parse the JSON request body to extract objection request details
    const body = await request.json();
    const { 
      objection_type,       // Type of objection ('direct_marketing', 'legitimate_interests', 'public_task', 'scientific_research')
      processing_purposes,  // Specific processing purposes being objected to (array)
      reason,              // User's reason for the objection
      legal_basis,         // Legal basis for the objection (defaults to Article 21 GDPR)
      data_categories      // Categories of data involved in the objection (optional)
    } = body;

    // Validate that all required fields are present in the request
    // These fields are mandatory for processing any objection request
    if (!objection_type || !processing_purposes || !reason) {
      return NextResponse.json(
        { error: "Missing required fields: objection_type, processing_purposes, reason" },
        { status: 400 }
      );
    }

    // Extract user and request metadata for audit logging and security
    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';  // User's IP address
    const userAgent = request.headers.get('user-agent') || 'unknown';      // User's browser/client info

    // Create a structured objection request object for audit logging
    // This contains all necessary information to track and process the objection
    const objectionRequest = {
      user_id: userId,
      request_type: 'objection',
      status: 'pending',                                                    // Initial status
      details: {
        objection_type,                                                     // Type of objection being made
        // Ensure processing_purposes is always an array for consistent processing
        processing_purposes: Array.isArray(processing_purposes) ? processing_purposes : [processing_purposes],
        // Ensure data_categories is always an array (can be empty if not specified)
        data_categories: Array.isArray(data_categories) ? data_categories : [data_categories],
        reason,                                                             // User's stated reason for objection
        legal_basis: legal_basis || 'article_21_gdpr',                      // Legal basis (defaults to GDPR Article 21)
        grounds: getObjectionGrounds(objection_type, reason)                // Specific legal grounds determined from objection type and reason
      },
      ip_address: clientIP,                                                 // For security audit trail
      user_agent: userAgent                                                 // For security audit trail
    };

    // Insert the objection request into the audit_logs table
    // This creates a permanent record of the user's objection for compliance purposes
    const { data, error } = await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'objection_request',                                        // Action type for filtering
        resource_type: 'data_processing',                                   // Type of resource being objected to
        details: objectionRequest.details,                                  // Full objection details
        ip_address: clientIP,
        user_agent: userAgent
      })
      .select()                                                             // Return the inserted record
      .single();                                                            // Expect only one record

    // If database insertion fails, throw error to be caught by outer try-catch
    if (error) {
      throw error;
    }

    // Handle direct marketing objections immediately as required by GDPR Article 21(3)
    // Direct marketing objections must result in immediate cessation of processing
    if (objection_type === 'direct_marketing') {
      try {
        // Immediately withdraw marketing consent by setting withdrawal date
        // This stops all marketing communications as required by law
        await supabaseAdmin
          .from('user_consents')
          .update({ withdrawal_date: new Date().toISOString() })            // Mark consent as withdrawn now
          .eq('user_id', userId)                                            // Only for this user
          .eq('consent_type', 'marketing_emails')                           // Specifically marketing emails
          .is('withdrawal_date', null);                                     // Only active consents (not already withdrawn)

        // Update cookie consent to disable marketing cookies
        // This ensures marketing tracking also stops immediately
        await supabaseAdmin
          .from('cookie_consents')
          .insert({
            user_id: userId,
            necessary_cookies: true,                                        // Keep necessary cookies (required for site function)
            analytics_cookies: true,                                        // Keep analytics unless specifically objected to
            marketing_cookies: false,                                       // Disable marketing cookies immediately
            consent_date: new Date().toISOString(),                         // When this consent change was made
            ip_address: clientIP,
            user_agent: userAgent
          });

        // Create an audit log entry documenting the immediate objection implementation
        // This is crucial for demonstrating GDPR compliance
        await supabaseAdmin
          .from('audit_logs')
          .insert({
            user_id: userId,
            action: 'objection_implemented',                                // Mark as implemented
            resource_type: 'marketing_processing',                          // Type of processing stopped
            details: {
              objection_type: 'direct_marketing',                           // Specific objection type
              implemented_immediately: true,                                // Flag for immediate implementation
              stopped_processing: ['marketing_emails', 'promotional_communications', 'marketing_cookies'], // What was stopped
              legal_requirement: 'GDPR Article 21(3) - immediate cessation required' // Legal basis for immediate action
            },
            ip_address: clientIP,
            user_agent: userAgent
          });

        // Return success response with immediate completion status
        return NextResponse.json({
          success: true,
          message: "Direct marketing objection processed successfully. All marketing communications have been stopped immediately.",
          request_id: data.id,                                              // Reference to original request
          status: 'completed',                                              // Status is completed immediately
          immediate_action: true,                                           // Flag indicating immediate action taken
          stopped_processing: ['marketing_emails', 'promotional_communications', 'marketing_cookies'] // What processing was stopped
        });
      } catch (objectionError) {
        // If immediate objection implementation fails, log the error but continue with manual review
        // This ensures the user's objection isn't lost even if auto-implementation fails
        console.error('Immediate objection implementation failed:', objectionError);
        // Continue with manual review process below
      }
    }

    // For other objection types that require manual review, prepare appropriate response
    // Different objection types have different legal requirements and timelines
    const responseMessage = getObjectionResponseMessage(objection_type);
    
    // Set estimated completion time based on objection type
    // Direct marketing: 1 day (though should be immediate), Others: 30 days (GDPR standard)
    const estimatedCompletion = objection_type === 'direct_marketing' 
      ? new Date(Date.now() + 24 * 60 * 60 * 1000)                         // 1 day for marketing objections
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);                   // 30 days for other objections

    // Log the creation of the objection request for monitoring purposes
    console.log(`Objection request created for user: ${userId}, type: ${objection_type}`);

    // Return response for objections that require manual review and assessment
    return NextResponse.json({
      success: true,
      message: responseMessage,                                             // Appropriate message based on objection type
      request_id: data.id,                                                  // Unique ID for tracking the request
      status: 'pending',                                                    // Status indicates manual review needed
      estimated_completion: estimatedCompletion.toISOString(),              // When user can expect a response
      next_steps: getNextSteps(objection_type)                              // Clear communication about what happens next
    });

  } catch (error: any) {
    // Handle any errors that occur during the objection request process
    // Log the error for debugging while returning a generic error to the user
    console.error('Objection request error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/gdpr/objection
 * 
 * Get user's objection request history and current objections
 * 
 * This endpoint allows users to view all their objection requests and see
 * which objections are currently active. This transparency is important
 * for GDPR compliance and user control over their data processing.
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user to ensure they can only see their own objections
    const session = await auth.api.getSession({ headers: headers() });
    
    // Return 401 if user is not authenticated
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Query the audit_logs table to get all objection-related entries for this user
    // We look for requests, implementations, and any overrides of objections
    const { data: requests, error } = await supabaseAdmin
      .from('audit_logs')
      .select('*')                                                          // Get all columns
      .eq('user_id', userId)                                                // Only this user's requests
      .in('action', ['objection_request', 'objection_implemented', 'objection_overridden']) // All objection actions
      .order('created_at', { ascending: false });                          // Most recent first

    // Handle database query errors
    if (error) {
      throw error;
    }

    // Process the requests to identify currently active objections
    // Active objections are those that have been implemented and not overridden
    const activeObjections = (requests || [])
      .filter(req => req.action === 'objection_implemented')                // Only implemented objections
      .map(req => ({
        id: req.id,                                                         // Unique identifier
        objection_type: req.details?.objection_type,                        // Type of objection
        implemented_date: req.created_at,                                   // When it was implemented
        stopped_processing: req.details?.stopped_processing || []           // What processing was stopped
      }));

    // Return comprehensive objection information
    return NextResponse.json({
      success: true,
      requests: requests || [],                                             // All objection-related requests
      active_objections: activeObjections,                                  // Currently active objections
      objection_status: activeObjections.length > 0 ? 'active' : 'none'    // Overall status summary
    });

  } catch (error: any) {
    // Handle any errors that occur while fetching objection information
    console.error('Get objection requests error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Helper function to determine legal grounds for objection based on objection type and reason
 * 
 * GDPR Article 21 provides different grounds for objection depending on the type:
 * - Article 21(2): Direct marketing (absolute right)
 * - Article 21(1): Legitimate interests and public task (balancing test required)
 * - Article 21(6): Scientific research (public interest assessment required)
 * 
 * @param objectionType - The type of objection being made
 * @param reason - The user's stated reason for the objection
 * @returns Array of applicable legal grounds
 */
function getObjectionGrounds(objectionType: string, reason: string): string[] {
  const grounds = [];
  
  switch (objectionType) {
    case 'direct_marketing':
      // Article 21(2) - Absolute right to object to direct marketing
      grounds.push('article_21_2_direct_marketing');
      break;
    case 'legitimate_interests':
      // Article 21(1) - Right to object to legitimate interests processing
      grounds.push('article_21_1_legitimate_interests');
      // Check if user mentions their particular situation (required for this objection type)
      if (reason.toLowerCase().includes('particular situation')) {
        grounds.push('particular_situation');
      }
      break;
    case 'public_task':
      // Article 21(1) - Right to object to public task processing
      grounds.push('article_21_1_public_task');
      break;
    case 'scientific_research':
      // Article 21(6) - Right to object to scientific research processing
      grounds.push('article_21_6_scientific_research');
      break;
    default:
      // Fallback for any other objection types
      grounds.push('general_objection');
  }
  
  return grounds;
}

/**
 * Helper function to generate appropriate response messages based on objection type
 * 
 * Different objection types have different legal requirements and user expectations,
 * so we provide specific messaging for each type to set proper expectations.
 * 
 * @param objectionType - The type of objection being made
 * @returns Appropriate response message for the objection type
 */
function getObjectionResponseMessage(objectionType: string): string {
  switch (objectionType) {
    case 'direct_marketing':
      // Direct marketing objections must be honored immediately (GDPR Article 21(3))
      return "Direct marketing objection submitted. Marketing communications will be stopped immediately as required by GDPR.";
    case 'legitimate_interests':
      // Legitimate interests objections require balancing test (GDPR Article 21(1))
      return "Objection to legitimate interests processing submitted. We will assess whether we have compelling legitimate grounds that override your interests.";
    case 'public_task':
      // Public task objections require assessment of particular situation (GDPR Article 21(1))
      return "Objection to public task processing submitted. We will review your particular situation and grounds for objection.";
    case 'scientific_research':
      // Scientific research objections require public interest assessment (GDPR Article 21(6))
      return "Objection to scientific research processing submitted. We will assess whether the research is necessary for public interest reasons.";
    default:
      // Generic message for any other objection types
      return "Your objection has been submitted successfully. We will review and respond within the required timeframe.";
  }
}

/**
 * Helper function to provide clear next steps based on objection type
 * 
 * Users need to understand what will happen after they submit their objection.
 * Different objection types have different processes and outcomes.
 * 
 * @param objectionType - The type of objection being made
 * @returns Array of next steps specific to the objection type
 */
function getNextSteps(objectionType: string): string[] {
  // Common steps that apply to most objection types
  const commonSteps = [
    "We will review your objection and legal grounds",
    "You will be notified of our decision and any actions taken"
  ];

  switch (objectionType) {
    case 'direct_marketing':
      // Direct marketing objections have immediate effect
      return [
        "Marketing communications stopped immediately",                     // Immediate action required by law
        ...commonSteps
      ];
    case 'legitimate_interests':
      // Legitimate interests objections require balancing test
      return [
        "We will assess our legitimate interests against your rights",      // Legal requirement for balancing test
        "Processing may continue if we demonstrate compelling legitimate grounds", // Possible outcome
        ...commonSteps
      ];
    default:
      // Generic next steps for other objection types
      return commonSteps;
  }
}
