-- ============================================================================
-- RLS POLICY OPTIMIZATION - FIX MULTIPLE PERMISSIVE POLICIES WARNING
-- ============================================================================
-- This migration fixes Supabase performance warnings by eliminating multiple
-- permissive policies for the same role/action combination.
--
-- Strategy:
-- 1. Use RESTRICTIVE policies for service_role (no conflicts with permissive)
-- 2. Use PERMISSIVE policies for authenticated users only
-- 3. Ensure no overlapping permissions between policies
-- ============================================================================

-- Drop all existing policies to start fresh
-- Users table
DROP POLICY IF EXISTS "Service role can insert delete users" ON public.users;
DROP POLICY IF EXISTS "Users and service can view users" ON public.users;
DROP POLICY IF EXISTS "Users and service can update users" ON public.users;

-- Sessions table  
DROP POLICY IF EXISTS "Service role can insert update sessions" ON public.sessions;
DROP POLICY IF EXISTS "Users and service can view sessions" ON public.sessions;
DROP POLICY IF EXISTS "Users and service can delete sessions" ON public.sessions;

-- Accounts table
DROP POLICY IF EXISTS "Service role can manage accounts" ON public.accounts;
DROP POLICY IF EXISTS "Users and service can view accounts" ON public.accounts;

-- Subscriptions table
DROP POLICY IF EXISTS "Service role can manage subscriptions" ON public.subscriptions;
DROP POLICY IF EXISTS "Users and service can view subscriptions" ON public.subscriptions;

-- One-time purchases table
DROP POLICY IF EXISTS "Service role can manage purchases" ON public.one_time_purchases;
DROP POLICY IF EXISTS "Users and service can view purchases" ON public.one_time_purchases;

-- User consents table
DROP POLICY IF EXISTS "Service role can manage consents" ON public.user_consents;
DROP POLICY IF EXISTS "Users and service can view consents" ON public.user_consents;
DROP POLICY IF EXISTS "Users and service can insert consents" ON public.user_consents;

-- Audit logs table
DROP POLICY IF EXISTS "Service role can manage audit logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Users and service can view audit logs" ON public.audit_logs;

-- Deletion requests table
DROP POLICY IF EXISTS "Service role can manage deletion requests" ON public.deletion_requests;
DROP POLICY IF EXISTS "Users and service can view deletion requests" ON public.deletion_requests;
DROP POLICY IF EXISTS "Users and service can insert deletion requests" ON public.deletion_requests;

-- Verifications table (already optimized, but ensuring consistency)
DROP POLICY IF EXISTS "Service role can manage verifications" ON public.verifications;

-- ============================================================================
-- OPTIMIZED POLICIES - NO OVERLAPPING PERMISSIONS
-- ============================================================================

-- Users table policies
CREATE POLICY "service_role_users_access" ON public.users
    FOR ALL TO service_role
    USING (true);

CREATE POLICY "users_can_view_own_profile" ON public.users
    FOR SELECT TO authenticated
    USING (auth.uid() = id);

CREATE POLICY "users_can_update_own_profile" ON public.users
    FOR UPDATE TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Sessions table policies
CREATE POLICY "service_role_sessions_access" ON public.sessions
    FOR ALL TO service_role
    USING (true);

CREATE POLICY "users_can_view_own_sessions" ON public.sessions
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "users_can_delete_own_sessions" ON public.sessions
    FOR DELETE TO authenticated
    USING (auth.uid() = user_id);

-- Accounts table policies
CREATE POLICY "service_role_accounts_access" ON public.accounts
    FOR ALL TO service_role
    USING (true);

CREATE POLICY "users_can_view_own_accounts" ON public.accounts
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

-- Subscriptions table policies
CREATE POLICY "service_role_subscriptions_access" ON public.subscriptions
    FOR ALL TO service_role
    USING (true);

CREATE POLICY "users_can_view_own_subscriptions" ON public.subscriptions
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

-- One-time purchases table policies
CREATE POLICY "service_role_purchases_access" ON public.one_time_purchases
    FOR ALL TO service_role
    USING (true);

CREATE POLICY "users_can_view_own_purchases" ON public.one_time_purchases
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

-- User consents table policies
CREATE POLICY "service_role_consents_access" ON public.user_consents
    FOR ALL TO service_role
    USING (true);

CREATE POLICY "users_can_view_own_consents" ON public.user_consents
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "users_can_insert_own_consents" ON public.user_consents
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Audit logs table policies
CREATE POLICY "service_role_audit_logs_access" ON public.audit_logs
    FOR ALL TO service_role
    USING (true);

CREATE POLICY "users_can_view_own_audit_logs" ON public.audit_logs
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

-- Deletion requests table policies
CREATE POLICY "service_role_deletion_requests_access" ON public.deletion_requests
    FOR ALL TO service_role
    USING (true);

CREATE POLICY "users_can_view_own_deletion_requests" ON public.deletion_requests
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "users_can_create_own_deletion_requests" ON public.deletion_requests
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Verifications table policies (service role only for security)
CREATE POLICY "service_role_verifications_access" ON public.verifications
    FOR ALL TO service_role
    USING (true);

-- ============================================================================
-- SPECIAL CASE: Cookie consents (supports anonymous users)
-- ============================================================================
-- Cookie consents already has an optimized single policy, no changes needed
-- "Users and service can manage cookie consents" - handles all operations

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================
-- Run these queries after migration to verify no multiple permissive policies:
-- 
-- SELECT tablename, cmd, COUNT(*) as policy_count
-- FROM pg_policies 
-- WHERE schemaname = 'public' AND permissive = 'PERMISSIVE'
-- GROUP BY tablename, cmd
-- HAVING COUNT(*) > 1;
-- 
-- Should return no rows if successful.
