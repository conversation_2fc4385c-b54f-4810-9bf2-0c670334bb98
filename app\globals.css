@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 80%;
    --accent-foreground: 0 0% 90%;
    --destructive: 0 0% 45%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 80%;
    --radius: 0px;
    --chart-1: 0 0% 20%;
    --chart-2: 0 0% 30%;
    --chart-3: 0 0% 40%;
    --chart-4: 0 0% 50%;
    --chart-5: 0 0% 60%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 80%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 0% 63.9%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 0 0% 80%;
    --chart-2: 0 0% 70%;
    --chart-3: 0 0% 60%;
    --chart-4: 0 0% 50%;
    --chart-5: 0 0% 40%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Subtle grid pattern background for visual texture */
    background-image: repeating-linear-gradient(
      0deg,
      rgba(128, 128, 128, 0.05) 0px,
      rgba(128, 128, 128, 0.05) 1px,
      transparent 1px,
      transparent 2px
    );
  }
  
  /* Selection styling for better readability and terminal-like aesthetics */
  ::selection {
    background: rgba(128, 128, 128, 0.3);
    color: rgb(200, 200, 200);
  }
  
  /* Custom scrollbar styling for modern browsers */
  ::-webkit-scrollbar {
    width: 8px; /* Narrow scrollbar for minimal visual impact */
    height: 8px;
  }

  /* Dark scrollbar track matching theme */
  ::-webkit-scrollbar-track {
    background: hsl(0 0% 3.9%);
  }

  /* Title font weight overrides - Make Inter fonts regular for titles */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 400 !important; /* Regular weight for all headings */
  }

  /* Override specific title classes to use regular weight */
  .font-bold h1, .font-bold h2, .font-bold h3, .font-bold h4, .font-bold h5, .font-bold h6,
  .font-semibold h1, .font-semibold h2, .font-semibold h3, .font-semibold h4, .font-semibold h5, .font-semibold h6,
  .font-medium h1, .font-medium h2, .font-medium h3, .font-medium h4, .font-medium h5, .font-medium h6 {
    font-weight: 400 !important; /* Regular weight */
  }

  /* Override heading elements with font weight classes */
  h1.font-bold, h1.font-semibold, h1.font-medium,
  h2.font-bold, h2.font-semibold, h2.font-medium,
  h3.font-bold, h3.font-semibold, h3.font-medium,
  h4.font-bold, h4.font-semibold, h4.font-medium,
  h5.font-bold, h5.font-semibold, h5.font-medium,
  h6.font-bold, h6.font-semibold, h6.font-medium {
    font-weight: 400 !important; /* Regular weight */
  }

  /* Override component-specific title styling */
  [class*="CardTitle"], .card-title,
  [class*="ToastTitle"], .toast-title,
  [class*="Heading"], .heading {
    font-weight: 400 !important; /* Regular weight for component titles */
  }

  /* Override any title-like elements with font-mono class */
  .font-mono h1, .font-mono h2, .font-mono h3, .font-mono h4, .font-mono h5, .font-mono h6 {
    font-weight: 400 !important; /* Regular weight for mono titles */
  }
  
  /* Subtle scrollbar thumb with hover effect */
  ::-webkit-scrollbar-thumb {
    background: rgba(128, 128, 128, 0.2);
    border-radius: 0; /* Sharp edges for modern look */
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(128, 128, 128, 0.3);
  }
}