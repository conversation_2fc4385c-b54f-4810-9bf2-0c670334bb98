import { ModernDashboardLayout } from "@/components/dashboard/modern-layout";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { ConsentManagementDashboard } from "@/components/consent/consent-management-dashboard";
import { <PERSON>ting<PERSON>, <PERSON>, <PERSON>, Palette, Globe } from "lucide-react";

export default function SettingsPage() {
  return (
    <ModernDashboardLayout
      title="Settings"
      subtitle="Configure your application and privacy preferences"
    >
      <div className="space-y-6">
        {/* Consent Management Dashboard */}
        <ConsentManagementDashboard />

        {/* Other Settings - Coming Soon */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
              <Settings className="w-5 h-5 text-[#ffbe98]" />
              Additional Settings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Settings className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-200 mb-2">
                More Settings Coming Soon
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400 max-w-md mx-auto">
                Additional configuration options are being developed.
              </p>
              <div className="mt-6 flex items-center justify-center gap-4 text-sm text-neutral-500">
                <div className="flex items-center gap-2">
                  <Bell className="w-4 h-4" />
                  <span>Notifications</span>
                </div>
                <div className="flex items-center gap-2">
                  <Palette className="w-4 h-4" />
                  <span>Appearance</span>
                </div>
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  <span>Localization</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernDashboardLayout>
  );
}
