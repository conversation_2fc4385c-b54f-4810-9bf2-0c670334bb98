"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Cookie, 
  Shield, 
  BarChart3, 
  Target, 
  CheckCircle, 
  XCircle,
  Loader2,
  Settings,
  Info,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

interface CookieSettings {
  necessary_cookies: boolean;
  analytics_cookies: boolean;
  marketing_cookies: boolean;
  consent_date: string | null;
}

export function AccountCookieSettings() {
  const [cookieSettings, setCookieSettings] = useState<CookieSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Local state for toggles
  const [localSettings, setLocalSettings] = useState<{
    analytics_cookies: boolean;
    marketing_cookies: boolean;
  }>({
    analytics_cookies: false,
    marketing_cookies: false
  });

  useEffect(() => {
    fetchCookieSettings();
  }, []);

  useEffect(() => {
    // Check if local settings differ from server settings
    if (cookieSettings) {
      const hasChanges = 
        localSettings.analytics_cookies !== cookieSettings.analytics_cookies ||
        localSettings.marketing_cookies !== cookieSettings.marketing_cookies;
      setHasChanges(hasChanges);
    }
  }, [localSettings, cookieSettings]);

  const fetchCookieSettings = async () => {
    try {
      const response = await fetch('/api/gdpr/cookie-consent');
      
      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Please sign in to manage cookie settings');
          return;
        }
        throw new Error('Failed to fetch cookie settings');
      }

      const data = await response.json();
      const settings = data.consent || {
        necessary_cookies: true,
        analytics_cookies: false,
        marketing_cookies: false,
        consent_date: null
      };

      setCookieSettings(settings);
      setLocalSettings({
        analytics_cookies: settings.analytics_cookies,
        marketing_cookies: settings.marketing_cookies
      });
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch cookie settings');
    } finally {
      setIsLoading(false);
    }
  };

  const updateCookieSettings = async () => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/gdpr/cookie-consent', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          necessary_cookies: true, // Always required
          analytics_cookies: localSettings.analytics_cookies,
          marketing_cookies: localSettings.marketing_cookies,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update cookie settings');
      }

      toast.success('Cookie preferences updated successfully');
      await fetchCookieSettings(); // Refresh data
      setHasChanges(false);
    } catch (error: any) {
      toast.error(error.message || 'Failed to update cookie settings');
    } finally {
      setIsUpdating(false);
    }
  };

  const resetToDefaults = () => {
    setLocalSettings({
      analytics_cookies: false,
      marketing_cookies: false
    });
  };

  const getStatusBadge = (enabled: boolean, required: boolean = false) => {
    if (required) {
      return (
        <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          <Shield className="w-3 h-3 mr-1" />
          Required
        </Badge>
      );
    }
    
    return enabled ? (
      <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
        <CheckCircle className="w-3 h-3 mr-1" />
        Enabled
      </Badge>
    ) : (
      <Badge variant="outline" className="text-gray-600">
        <XCircle className="w-3 h-3 mr-1" />
        Disabled
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span className="ml-2">Loading cookie settings...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!cookieSettings) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <AlertTriangle className="w-8 h-8 mx-auto mb-2 text-yellow-500" />
            <p>Failed to load cookie settings</p>
            <Button onClick={fetchCookieSettings} className="mt-2" size="sm">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Cookie className="w-5 h-5 text-orange-500" />
            Cookie Settings
          </div>
          <Link href="/privacy/consent">
            <Button variant="ghost" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Advanced
            </Button>
          </Link>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Necessary Cookies - Always Required */}
        <div className="flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-900">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Shield className="w-4 h-4 text-blue-500" />
              <h4 className="font-medium">Necessary Cookies</h4>
              {getStatusBadge(true, true)}
            </div>
            <p className="text-sm text-muted-foreground">
              Essential for website functionality and security
            </p>
          </div>
          <Switch checked={true} disabled />
        </div>

        {/* Analytics Cookies - Optional */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <BarChart3 className="w-4 h-4 text-green-500" />
              <h4 className="font-medium">Analytics Cookies</h4>
              {getStatusBadge(localSettings.analytics_cookies)}
            </div>
            <p className="text-sm text-muted-foreground">
              Help us understand how you use our website
            </p>
          </div>
          <Switch
            checked={localSettings.analytics_cookies}
            onCheckedChange={(checked) => 
              setLocalSettings(prev => ({ ...prev, analytics_cookies: checked }))
            }
            disabled={isUpdating}
          />
        </div>

        {/* Marketing Cookies - Optional */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Target className="w-4 h-4 text-purple-500" />
              <h4 className="font-medium">Marketing Cookies</h4>
              {getStatusBadge(localSettings.marketing_cookies)}
            </div>
            <p className="text-sm text-muted-foreground">
              Used for personalized advertising and campaigns
            </p>
          </div>
          <Switch
            checked={localSettings.marketing_cookies}
            onCheckedChange={(checked) => 
              setLocalSettings(prev => ({ ...prev, marketing_cookies: checked }))
            }
            disabled={isUpdating}
          />
        </div>

        {/* Action Buttons */}
        {hasChanges && (
          <div className="flex gap-2 pt-2 border-t">
            <Button 
              onClick={updateCookieSettings} 
              disabled={isUpdating}
              className="flex-1"
            >
              {isUpdating ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Cookie className="w-4 h-4 mr-2" />
              )}
              Save Changes
            </Button>
            <Button 
              variant="outline" 
              onClick={resetToDefaults}
              disabled={isUpdating}
            >
              Reset
            </Button>
          </div>
        )}

        {/* Last Updated Info */}
        {cookieSettings.consent_date && (
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <Info className="w-3 h-3" />
              Last updated: {new Date(cookieSettings.consent_date).toLocaleDateString()}
            </p>
          </div>
        )}

        {/* Info Box */}
        <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <Info className="w-4 h-4 text-blue-500 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                About Cookie Settings
              </p>
              <p className="text-blue-800 dark:text-blue-200">
                You can change these preferences at any time. Changes take effect immediately 
                and will be applied to your current session.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
