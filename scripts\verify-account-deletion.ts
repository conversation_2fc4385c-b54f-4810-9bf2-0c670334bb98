/**
 * Account Deletion Verification Script
 * 
 * This script verifies that account deletion is working correctly
 * and is GDPR compliant by checking all tables for user data.
 */

import { supabaseAdmin } from '@/lib/supabase';

interface OrphanedRecord {
  table: string;
  count: number;
  userIds: string[];
}

interface VerificationResult {
  success: boolean;
  orphanedRecords: OrphanedRecord[];
  userCount: number;
  issues: string[];
  creemIntegration: {
    configured: boolean;
    apiKeyPresent: boolean;
  };
}

/**
 * Verify that account deletion is working correctly
 */
export async function verifyAccountDeletion(deletedUserId?: string): Promise<VerificationResult> {
  const result: VerificationResult = {
    success: true,
    orphanedRecords: [],
    userCount: 0,
    issues: [],
    creemIntegration: {
      configured: !!process.env.CREEM_API_KEY,
      apiKeyPresent: !!process.env.CREEM_API_KEY
    }
  };

  try {
    // 1. Check current users count
    const { data: users, error: usersError } = await supabaseAdmin
      .from('users')
      .select('id, email, created_at');

    if (usersError) {
      result.issues.push(`Failed to query users table: ${usersError.message}`);
      result.success = false;
      return result;
    }

    result.userCount = users?.length || 0;
    console.log(`Current users in system: ${result.userCount}`);

    // 2. Check for orphaned records in all tables
    const tablesToCheck = [
      'sessions',
      'accounts',
      'subscriptions',
      'one_time_purchases',
      'verifications',
      'user_consents',
      'audit_logs',
      'deletion_requests',
      'cookie_consents'
    ];

    for (const table of tablesToCheck) {
      try {
        let query;

        if (table === 'verifications') {
          // Verifications table uses 'identifier' (email) instead of user_id
          if (deletedUserId) {
            // Get the email of the deleted user first
            const { data: userData } = await supabaseAdmin
              .from('users')
              .select('email')
              .eq('id', deletedUserId)
              .single();

            if (userData?.email) {
              query = supabaseAdmin
                .from(table as any)
                .select('*')
                .eq('identifier', userData.email);
            }
          }
        } else if (table === 'cookie_consents') {
          // Cookie consents can have null user_id for anonymous users
          query = supabaseAdmin
            .from(table as any)
            .select('user_id')
            .not('user_id', 'is', null);
        } else {
          // Standard user_id based tables
          query = supabaseAdmin
            .from(table as any)
            .select('user_id');
        }

        if (query) {
          const { data: records, error } = await query;

          if (error) {
            result.issues.push(`Failed to query ${table}: ${error.message}`);
            continue;
          }

          if (records && records.length > 0) {
            // Check if these records have valid users
            const userIds = table === 'verifications'
              ? [] // Skip user validation for verifications
              : Array.from(new Set((records as any[]).map((r: any) => r.user_id).filter(Boolean)));

            if (userIds.length > 0) {
              const { data: validUsers } = await supabaseAdmin
                .from('users')
                .select('id')
                .in('id', userIds);

              const validUserIds = new Set(validUsers?.map(u => u.id) || []);
              const orphanedUserIds = userIds.filter(id => !validUserIds.has(id));

              if (orphanedUserIds.length > 0) {
                result.orphanedRecords.push({
                  table,
                  count: orphanedUserIds.length,
                  userIds: orphanedUserIds
                });
              }
            }
          }
        }
      } catch (error) {
        result.issues.push(`Error checking ${table}: ${error}`);
      }
    }

    // 3. Check if specific user was properly deleted
    if (deletedUserId) {
      const { data: deletedUser } = await supabaseAdmin
        .from('users')
        .select('id')
        .eq('id', deletedUserId)
        .single();

      if (deletedUser) {
        result.issues.push(`User ${deletedUserId} still exists in users table!`);
        result.success = false;
      }
    }

    // 4. Determine overall success
    if (result.orphanedRecords.length > 0 || result.issues.length > 0) {
      result.success = false;
    }

    return result;

  } catch (error) {
    result.issues.push(`Verification failed: ${error}`);
    result.success = false;
    return result;
  }
}

/**
 * Clean up orphaned records (use with caution)
 */
export async function cleanupOrphanedRecords(): Promise<void> {
  console.log('Starting cleanup of orphaned records...');

  const verification = await verifyAccountDeletion();

  for (const orphaned of verification.orphanedRecords) {
    try {
      console.log(`Cleaning up ${orphaned.count} orphaned records from ${orphaned.table}`);

      const { error } = await supabaseAdmin
        .from(orphaned.table as any)
        .delete()
        .in('user_id', orphaned.userIds);

      if (error) {
        console.error(`Failed to cleanup ${orphaned.table}:`, error);
      } else {
        console.log(`Successfully cleaned up ${orphaned.table}`);
      }
    } catch (error) {
      console.error(`Error cleaning up ${orphaned.table}:`, error);
    }
  }

  console.log('Cleanup completed');
}

// CLI usage
if (require.main === module) {
  const command = process.argv[2];
  const userId = process.argv[3];

  if (command === 'verify') {
    verifyAccountDeletion(userId).then(result => {
      console.log('\n=== ACCOUNT DELETION VERIFICATION ===');
      console.log(`Success: ${result.success}`);
      console.log(`Current users: ${result.userCount}`);
      console.log(`Orphaned records found: ${result.orphanedRecords.length}`);
      console.log(`Issues: ${result.issues.length}`);

      if (result.orphanedRecords.length > 0) {
        console.log('\nOrphaned Records:');
        result.orphanedRecords.forEach(record => {
          console.log(`- ${record.table}: ${record.count} records`);
        });
      }

      if (result.issues.length > 0) {
        console.log('\nIssues:');
        result.issues.forEach(issue => console.log(`- ${issue}`));
      }

      process.exit(result.success ? 0 : 1);
    });
  } else if (command === 'cleanup') {
    cleanupOrphanedRecords().then(() => {
      console.log('Cleanup completed');
      process.exit(0);
    });
  } else {
    console.log('Usage:');
    console.log('  bun run scripts/verify-account-deletion.ts verify [userId]');
    console.log('  bun run scripts/verify-account-deletion.ts cleanup');
    process.exit(1);
  }
}
