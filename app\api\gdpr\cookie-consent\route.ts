import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * GET /api/gdpr/cookie-consent
 * 
 * Get user's current cookie consent preferences
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get the most recent cookie consent for the user
    const { data: cookieConsent, error } = await supabaseAdmin
      .from('cookie_consents')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw error;
    }

    return NextResponse.json({
      user_id: userId,
      consent: cookieConsent || {
        necessary_cookies: true,
        analytics_cookies: false,
        marketing_cookies: false,
        consent_date: null
      }
    });

  } catch (error: any) {
    console.error('Get cookie consent error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/gdpr/cookie-consent
 * 
 * Save user's cookie consent preferences
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    // Allow both authenticated and anonymous users to save cookie preferences
    const body = await request.json();
    const { 
      necessary_cookies = true, 
      analytics_cookies = false, 
      marketing_cookies = false,
      session_id 
    } = body;

    const userId = session?.user?.id || null;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Validate input
    if (typeof necessary_cookies !== 'boolean' || 
        typeof analytics_cookies !== 'boolean' || 
        typeof marketing_cookies !== 'boolean') {
      return NextResponse.json(
        { error: "Invalid consent data - must be boolean values" },
        { status: 400 }
      );
    }

    // Create consent record
    const consentRecord = {
      user_id: userId,
      session_id: session_id || null,
      necessary_cookies,
      analytics_cookies,
      marketing_cookies,
      consent_date: new Date().toISOString(),
      ip_address: clientIP,
      user_agent: userAgent
    };

    const { data, error } = await supabaseAdmin
      .from('cookie_consents')
      .insert(consentRecord)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log the consent change for audit purposes (only for authenticated users)
    if (userId) {
      await supabaseAdmin
        .from('audit_logs')
        .insert({
          user_id: userId,
          action: 'cookie_consent_update',
          resource_type: 'cookie_consents',
          details: {
            necessary_cookies,
            analytics_cookies,
            marketing_cookies,
            consent_method: 'cookie_banner'
          },
          ip_address: clientIP,
          user_agent: userAgent
        });
    }

    console.log(`Cookie consent saved for ${userId ? `user ${userId}` : 'anonymous user'}`);

    return NextResponse.json({
      success: true,
      message: "Cookie consent preferences saved successfully",
      consent: data
    });

  } catch (error: any) {
    console.error('Save cookie consent error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/gdpr/cookie-consent
 * 
 * Update existing cookie consent preferences
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      necessary_cookies = true, 
      analytics_cookies = false, 
      marketing_cookies = false 
    } = body;

    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Validate input
    if (typeof necessary_cookies !== 'boolean' || 
        typeof analytics_cookies !== 'boolean' || 
        typeof marketing_cookies !== 'boolean') {
      return NextResponse.json(
        { error: "Invalid consent data - must be boolean values" },
        { status: 400 }
      );
    }

    // Create new consent record (we keep history, don't update existing)
    const consentRecord = {
      user_id: userId,
      session_id: null,
      necessary_cookies,
      analytics_cookies,
      marketing_cookies,
      consent_date: new Date().toISOString(),
      ip_address: clientIP,
      user_agent: userAgent
    };

    const { data, error } = await supabaseAdmin
      .from('cookie_consents')
      .insert(consentRecord)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log the consent change
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'cookie_consent_update',
        resource_type: 'cookie_consents',
        details: {
          necessary_cookies,
          analytics_cookies,
          marketing_cookies,
          consent_method: 'settings_page'
        },
        ip_address: clientIP,
        user_agent: userAgent
      });

    console.log(`Cookie consent updated for user: ${userId}`);

    return NextResponse.json({
      success: true,
      message: "Cookie consent preferences updated successfully",
      consent: data
    });

  } catch (error: any) {
    console.error('Update cookie consent error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/gdpr/cookie-consent
 * 
 * Withdraw all cookie consents (reset to necessary only)
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Create a new consent record with only necessary cookies
    const consentRecord = {
      user_id: userId,
      session_id: null,
      necessary_cookies: true,
      analytics_cookies: false,
      marketing_cookies: false,
      consent_date: new Date().toISOString(),
      ip_address: clientIP,
      user_agent: userAgent
    };

    const { data, error } = await supabaseAdmin
      .from('cookie_consents')
      .insert(consentRecord)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log the consent withdrawal
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'cookie_consent_withdrawal',
        resource_type: 'cookie_consents',
        details: {
          action: 'withdrew_all_optional_cookies',
          reason: 'user_requested'
        },
        ip_address: clientIP,
        user_agent: userAgent
      });

    console.log(`Cookie consents withdrawn for user: ${userId}`);

    return NextResponse.json({
      success: true,
      message: "Optional cookie consents have been withdrawn",
      consent: data
    });

  } catch (error: any) {
    console.error('Withdraw cookie consent error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
