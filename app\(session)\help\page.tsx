import { ModernDashboardLayout } from "@/components/dashboard/modern-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { HelpCircle, MessageCircle, Book, Mail, ExternalLink } from "lucide-react";

export default function HelpPage() {
  return (
    <ModernDashboardLayout 
      title="Help & Support" 
      subtitle="Get assistance and find answers to your questions"
    >
      <div className="space-y-6">
        {/* Help Resources */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
                <Book className="w-5 h-5 text-[#ffbe98]" />
                Documentation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400 text-sm mb-4">
                Comprehensive guides and API documentation to help you get started.
              </p>
              <a 
                href="#" 
                className="inline-flex items-center gap-2 text-[#ffbe98] hover:text-[#ffbe98]/80 text-sm"
              >
                View Docs
                <ExternalLink className="w-3 h-3" />
              </a>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
                <MessageCircle className="w-5 h-5 text-[#ffbe98]" />
                Live Chat
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400 text-sm mb-4">
                Get instant help from our support team through live chat.
              </p>
              <button className="inline-flex items-center gap-2 text-[#ffbe98] hover:text-[#ffbe98]/80 text-sm">
                Start Chat
                <MessageCircle className="w-3 h-3" />
              </button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
                <Mail className="w-5 h-5 text-[#ffbe98]" />
                Email Support
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400 text-sm mb-4">
                Send us an email and we&apos;ll get back to you within 24 hours.
              </p>
              <a 
                href="mailto:<EMAIL>" 
                className="inline-flex items-center gap-2 text-[#ffbe98] hover:text-[#ffbe98]/80 text-sm"
              >
                Send Email
                <Mail className="w-3 h-3" />
              </a>
            </CardContent>
          </Card>
        </div>

        {/* FAQ Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
              <HelpCircle className="w-5 h-5 text-[#ffbe98]" />
              Frequently Asked Questions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border-b border-neutral-200 dark:border-neutral-700 pb-4">
                <h4 className="font-medium text-neutral-900 dark:text-neutral-200 mb-2">
                  How do I manage my billing and subscriptions?
                </h4>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Navigate to the Billing page to view your current subscriptions, manage payment methods, and explore available subscription plans.
                </p>
              </div>
              
              <div className="border-b border-neutral-200 dark:border-neutral-700 pb-4">
                <h4 className="font-medium text-neutral-900 dark:text-neutral-200 mb-2">
                  How do I manage customer subscriptions?
                </h4>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  You can view and manage all customer subscriptions from the Customers page. Use the filters to find specific subscriptions.
                </p>
              </div>
              
              <div className="border-b border-neutral-200 dark:border-neutral-700 pb-4">
                <h4 className="font-medium text-neutral-900 dark:text-neutral-200 mb-2">
                  How do I set up webhooks?
                </h4>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Webhooks can be configured in the Settings page under the Integrations section. Add your endpoint URL and select the events you want to receive.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-neutral-900 dark:text-neutral-200 mb-2">
                  How do I export my data?
                </h4>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Data export options are available in each section. Look for the download icon in the top-right corner of data tables.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernDashboardLayout>
  );
}
