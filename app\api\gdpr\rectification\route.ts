import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * POST /api/gdpr/rectification
 * 
 * GDPR Article 16 - Right to Rectification
 * Handle user requests to correct or update their personal data
 * 
 * This endpoint allows users to request corrections to their personal data
 * as guaranteed under GDPR Article 16. It supports both automatic approval
 * for certain fields and manual review processes for sensitive data.
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate the user using the session from request headers
    // This ensures only authenticated users can make rectification requests
    const session = await auth.api.getSession({ headers: headers() });
    
    // Return 401 Unauthorized if no valid session exists
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    // Parse the JSON request body to extract rectification request details
    const body = await request.json();
    const { 
      request_type,    // Type of rectification (e.g., 'profile_update', 'data_correction')
      current_data,    // Current value of the field (for audit purposes)
      requested_data,  // New value requested by the user
      reason,          // Optional reason for the rectification request
      field_name       // Name of the field to be corrected (e.g., 'name', 'email')
    } = body;

    // Validate that all required fields are present in the request
    // These fields are mandatory for processing any rectification request
    if (!request_type || !field_name || !requested_data) {
      return NextResponse.json(
        { error: "Missing required fields: request_type, field_name, requested_data" },
        { status: 400 }
      );
    }

    // Extract user and request metadata for audit logging
    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';  // User's IP address
    const userAgent = request.headers.get('user-agent') || 'unknown';      // User's browser/client info

    // Create a structured rectification request object for audit logging
    // This contains all the necessary information to track and process the request
    const rectificationRequest = {
      user_id: userId,
      request_type: 'rectification',
      status: 'pending',                                                    // Initial status
      details: {
        field_name,                                                         // Field to be corrected
        current_data,                                                       // Current value (for comparison)
        requested_data,                                                     // Requested new value
        reason: reason || 'User requested data correction',                 // Reason for correction
        request_type                                                        // Type of rectification request
      },
      ip_address: clientIP,                                                 // For security audit trail
      user_agent: userAgent                                                 // For security audit trail
    };

    // Insert the rectification request into the audit_logs table
    // This creates a permanent record of the user's request for compliance purposes
    const { data, error } = await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'rectification_request',                                    // Action type for filtering
        resource_type: 'user_data',                                         // Type of resource being modified
        resource_id: field_name,                                            // Specific field being corrected
        details: rectificationRequest.details,                              // Full request details
        ip_address: clientIP,
        user_agent: userAgent
      })
      .select()                                                             // Return the inserted record
      .single();                                                            // Expect only one record

    // If database insertion fails, throw error to be caught by outer try-catch
    if (error) {
      throw error;
    }

    // Define fields that can be automatically approved and updated without manual review
    // These are typically non-sensitive fields that users should be able to correct immediately
    const autoApprovableFields = ['name', 'email'];
    
    // Check if this request qualifies for automatic approval and processing
    if (autoApprovableFields.includes(field_name) && request_type === 'profile_update') {
      try {
        // Create dynamic update object with the field to be updated
        // This allows us to update any field name dynamically
        const updateData: any = {};
        updateData[field_name] = requested_data;
        
        // Perform the actual database update on the users table
        const { error: updateError } = await supabaseAdmin
          .from('users')
          .update(updateData)                                               // Update with new data
          .eq('id', userId);                                                // Only update this user's record

        // If the update was successful, log the completion
        if (!updateError) {
          // Create an audit log entry for the completed rectification
          // This is important for GDPR compliance and audit trails
          await supabaseAdmin
            .from('audit_logs')
            .insert({
              user_id: userId,
              action: 'rectification_completed',                            // Mark as completed
              resource_type: 'user_profile',                                // Specify what was updated
              resource_id: field_name,                                      // Which field was updated
              details: {
                field_updated: field_name,                                  // Field that was changed
                old_value: current_data,                                    // Previous value (for audit)
                new_value: requested_data,                                  // New value
                auto_approved: true                                         // Flag indicating automatic approval
              },
              ip_address: clientIP,
              user_agent: userAgent
            });

          // Return success response with immediate completion status
          return NextResponse.json({
            success: true,
            message: "Data rectification completed successfully",
            request_id: data.id,                                            // Reference to the original request
            status: 'completed',                                            // Status is completed immediately
            auto_approved: true                                             // Indicate this was auto-approved
          });
        }
      } catch (updateError) {
        // If automatic update fails, log the error but continue with manual review process
        // This ensures the user's request isn't lost even if auto-approval fails
        console.error('Auto-update failed:', updateError);
        // Continue with manual review process below
      }
    }

    // Log the creation of the rectification request for monitoring purposes
    console.log(`Rectification request created for user: ${userId}, field: ${field_name}`);

    // Return response for requests that require manual review
    // GDPR requires organizations to respond within 30 days (can be extended to 90 days in complex cases)
    return NextResponse.json({
      success: true,
      message: "Rectification request submitted successfully. We will review and process your request within 30 days.",
      request_id: data.id,                                                  // Unique ID for tracking the request
      status: 'pending',                                                    // Status indicates manual review needed
      estimated_completion: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now (GDPR compliance)
    });

  } catch (error: any) {
    // Handle any errors that occur during the rectification request process
    // Log the error for debugging while returning a generic error to the user
    console.error('Rectification request error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/gdpr/rectification
 * 
 * Get user's rectification request history
 * 
 * This endpoint allows users to view all their past rectification requests
 * and their current status. This transparency is important for GDPR compliance.
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user to ensure they can only see their own requests
    const session = await auth.api.getSession({ headers: headers() });
    
    // Return 401 if user is not authenticated
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Query the audit_logs table to get all rectification-related entries for this user
    // We look for both initial requests and completed rectifications
    const { data: requests, error } = await supabaseAdmin
      .from('audit_logs')
      .select('*')                                                          // Get all columns
      .eq('user_id', userId)                                                // Only this user's requests
      .in('action', ['rectification_request', 'rectification_completed'])   // Both request types
      .order('created_at', { ascending: false });                          // Most recent first

    // Handle database query errors
    if (error) {
      throw error;
    }

    // Return the user's rectification request history
    // Empty array if no requests found (requests || [] handles null case)
    return NextResponse.json({
      success: true,
      requests: requests || []                                              // All rectification requests for this user
    });

  } catch (error: any) {
    // Handle any errors that occur while fetching rectification requests
    console.error('Get rectification requests error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
